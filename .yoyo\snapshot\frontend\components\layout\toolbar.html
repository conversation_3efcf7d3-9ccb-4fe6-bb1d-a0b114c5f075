<!-- 顶部工具栏组件 -->
<div class="toolbar">
  <el-input
    v-model="searchKey"
    placeholder="搜索文件"
    prefix-icon="el-icon-search"
    style="width: 240px"
    clearable
    @input="handleSearch">
  </el-input>

  <el-date-picker
    v-model="dateRange"
    type="daterange"
    range-separator="至"
    start-placeholder="开始日期"
    end-placeholder="结束日期"
    :picker-options="pickerOptions">
  </el-date-picker>

  <!-- 知识库下拉选择框 -->
  <el-select
    v-model="selectedLibrary"
    placeholder="选择知识库"
    clearable
    style="width: 160px; margin-right: 10px;">
    <el-option
      v-for="kb in libraries"
      :key="kb.id"
      :label="kb.name"
      :value="kb.id">
    </el-option>
  </el-select>

  <el-select
    v-model="filterStatus"
    placeholder="是否审核"
    clearable
    style="width: 120px; margin-right: 10px;">
    <el-option label="未审核" value="0"></el-option>
    <el-option label="已审核" value="1"></el-option>
  </el-select>

  <el-button
    type="primary"
    icon="el-icon-search"
    @click="handleQuery">
    查询
  </el-button>
  
  <el-button
    type="primary"
    icon="el-icon-upload"
    @click="handleOpenImport">
    导入数据
  </el-button>

  <!-- 登录按钮 -->
  <el-button v-if="!sfilesh"
    type="info"
    icon="el-icon-user"
    @click="handleLoginClick('logon')"
    style="margin-left: auto;">
    登录
  </el-button>
  
  <el-button v-else
    type="primary"
    icon="el-icon-user"
    @click="handleLoginClick('exit')"
    style="margin-left: auto;">
    退出
  </el-button>
</div>
