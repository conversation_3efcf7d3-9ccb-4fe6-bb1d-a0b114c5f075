{% extends "base.html" %}

{% block title %}内容审核{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/frontend/static/css/audit.css">
{% endblock %}

{% block content %}
<div class="main-container d-flex">
  <!-- 侧边栏导航 -->
  {% include 'components/layout/sidebar.html' %}

  <!-- 右侧内容 -->
  <div class="main-content flex-grow-1">
    <!-- 顶部工具栏 -->
    <div class="toolbar p-3 bg-light border-bottom">
      <div class="d-flex align-items-center gap-3">
        <select id="selectedDataset" class="form-select" style="width: 200px;" onchange="AuditManager.loadCollections()">
          <option value="">选择数据集</option>
        </select>

        <select id="selectedCollection" class="form-select" style="width: 200px;" onchange="AuditManager.loadAuditData()">
          <option value="">选择集合</option>
        </select>

        <button type="button" class="btn btn-primary" onclick="AuditManager.loadAuditData()" id="queryBtn">
          <span class="spinner-border spinner-border-sm d-none" id="querySpinner"></span>
          查询
        </button>
        <button type="button" class="btn btn-success" onclick="AuditManager.batchAudit()" id="batchAuditBtn" disabled>
          <span class="spinner-border spinner-border-sm d-none" id="batchAuditSpinner"></span>
          批量审核
        </button>
      </div>
    </div>

    <!-- 审核数据展示 -->
    <div class="content-area p-3">
      <!-- 空状态 -->
      <div id="emptyState" class="empty-state text-center py-5 d-none">
        <i class="bi bi-file-earmark-check" style="font-size: 4rem; color: #6c757d;"></i>
        <p class="mt-3 text-muted">暂无待审核数据</p>
      </div>

      <!-- 审核数据列表 -->
      <div id="auditDataContainer" class="audit-data-list">
        <!-- 批量操作栏 -->
        <div class="batch-actions mb-3 p-3 bg-light rounded">
          <div class="d-flex align-items-center justify-content-between">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="selectAll" onchange="AuditManager.handleSelectAll(this.checked)">
              <label class="form-check-label" for="selectAll">全选</label>
            </div>
            <span class="selected-count text-muted">已选择 <span id="selectedCount">0</span> 项</span>
          </div>
        </div>

        <!-- 审核数据项容器 -->
        <div id="auditDataList" class="audit-data-items">
          <!-- 数据项将通过JavaScript动态生成 -->
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 编辑数据对话框 -->
<div class="modal fade" id="editModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">编辑审核数据</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="editForm">
          <div class="mb-3">
            <label for="editQ" class="form-label">问题内容</label>
            <textarea class="form-control" id="editQ" rows="6" placeholder="请输入问题内容"></textarea>
          </div>

          <div class="mb-3">
            <label for="editA" class="form-label">答案内容</label>
            <textarea class="form-control" id="editA" rows="4" placeholder="请输入答案内容"></textarea>
          </div>

          <div class="mb-3">
            <label for="editAuditNote" class="form-label">审核备注</label>
            <textarea class="form-control" id="editAuditNote" rows="3" placeholder="请输入审核备注"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="AuditManager.saveEdit()" id="saveEditBtn">
          <span class="spinner-border spinner-border-sm d-none" id="saveEditSpinner"></span>
          保存
        </button>
      </div>
    </div>
  </div>
</div>

<!-- 图片预览对话框 -->
<div class="modal fade" id="previewModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">图片预览</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body text-center">
        <img id="previewImage" src="" class="img-fluid" style="max-height: 70vh;">
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
      </div>
    </div>
  </div>
</div>

<!-- 拒绝原因对话框 -->
<div class="modal fade" id="rejectModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">拒绝原因</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="rejectForm">
          <div class="mb-3">
            <label for="rejectReason" class="form-label">拒绝原因</label>
            <select class="form-select" id="rejectReason">
              <option value="">请选择拒绝原因</option>
              <option value="inappropriate">内容不当</option>
              <option value="incorrect">信息错误</option>
              <option value="format">格式问题</option>
              <option value="duplicate">重复内容</option>
              <option value="other">其他</option>
            </select>
          </div>

          <div class="mb-3">
            <label for="rejectNote" class="form-label">详细说明</label>
            <textarea class="form-control" id="rejectNote" rows="4" placeholder="请输入详细说明"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-danger" onclick="AuditManager.confirmReject()" id="confirmRejectBtn">
          <span class="spinner-border spinner-border-sm d-none" id="rejectSpinner"></span>
          确认拒绝
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="/frontend/static/js/audit.js"></script>
{% endblock %}
