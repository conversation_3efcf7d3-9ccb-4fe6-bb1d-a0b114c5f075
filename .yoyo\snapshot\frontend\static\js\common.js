/**
 * 知识库管理系统 - 公共JavaScript函数
 */

// ==================== 全局配置 ====================
Vue.use(ELEMENT);

// ==================== 公共工具函数 ====================

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的大小
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 格式化时间
 * @param {string|Date} time 时间
 * @returns {string} 格式化后的时间
 */
function formatTime(time) {
  if (!time) return '';
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
}

/**
 * 截断文本
 * @param {string} str 原始文本
 * @param {number} length 最大长度
 * @returns {string} 截断后的文本
 */
function truncateText(str, length = 20) {
  if (!str) return '';
  return str.length > length ? str.slice(0, length) + '...' : str;
}

/**
 * 显示成功消息
 * @param {string} message 消息内容
 */
function showSuccess(message) {
  ELEMENT.Message.success(message);
}

/**
 * 显示错误消息
 * @param {string} message 消息内容
 */
function showError(message) {
  ELEMENT.Message.error(message);
}

/**
 * 显示警告消息
 * @param {string} message 消息内容
 */
function showWarning(message) {
  ELEMENT.Message.warning(message);
}

/**
 * 显示信息消息
 * @param {string} message 消息内容
 */
function showInfo(message) {
  ELEMENT.Message.info(message);
}

/**
 * 确认对话框
 * @param {string} message 确认消息
 * @param {string} title 标题
 * @returns {Promise} Promise对象
 */
function confirmDialog(message, title = '确认') {
  return ELEMENT.MessageBox.confirm(message, title, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  });
}

/**
 * 加载状态管理
 */
const LoadingManager = {
  instance: null,
  
  show(text = '加载中...') {
    this.instance = ELEMENT.Loading.service({
      lock: true,
      text: text,
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
  },
  
  hide() {
    if (this.instance) {
      this.instance.close();
      this.instance = null;
    }
  }
};

/**
 * HTTP请求封装
 */
const ApiClient = {
  /**
   * GET请求
   * @param {string} url 请求URL
   * @param {object} params 请求参数
   * @returns {Promise} Promise对象
   */
  async get(url, params = {}) {
    try {
      const response = await axios.get(url, { params });
      return response.data;
    } catch (error) {
      console.error('GET请求失败:', error);
      showError('请求失败: ' + (error.response?.data?.detail || error.message));
      throw error;
    }
  },

  /**
   * POST请求
   * @param {string} url 请求URL
   * @param {object} data 请求数据
   * @returns {Promise} Promise对象
   */
  async post(url, data = {}) {
    try {
      const response = await axios.post(url, data);
      return response.data;
    } catch (error) {
      console.error('POST请求失败:', error);
      showError('请求失败: ' + (error.response?.data?.detail || error.message));
      throw error;
    }
  },

  /**
   * 文件上传
   * @param {string} url 上传URL
   * @param {FormData} formData 表单数据
   * @param {function} onProgress 进度回调
   * @returns {Promise} Promise对象
   */
  async upload(url, formData, onProgress = null) {
    try {
      const config = {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      };
      
      if (onProgress) {
        config.onUploadProgress = (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(percentCompleted);
        };
      }
      
      const response = await axios.post(url, formData, config);
      return response.data;
    } catch (error) {
      console.error('文件上传失败:', error);
      showError('上传失败: ' + (error.response?.data?.detail || error.message));
      throw error;
    }
  }
};

/**
 * 本地存储管理
 */
const StorageManager = {
  /**
   * 设置本地存储
   * @param {string} key 键
   * @param {any} value 值
   */
  set(key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('设置本地存储失败:', error);
    }
  },

  /**
   * 获取本地存储
   * @param {string} key 键
   * @param {any} defaultValue 默认值
   * @returns {any} 存储的值
   */
  get(key, defaultValue = null) {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error('获取本地存储失败:', error);
      return defaultValue;
    }
  },

  /**
   * 删除本地存储
   * @param {string} key 键
   */
  remove(key) {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('删除本地存储失败:', error);
    }
  },

  /**
   * 清空本地存储
   */
  clear() {
    try {
      localStorage.clear();
    } catch (error) {
      console.error('清空本地存储失败:', error);
    }
  }
};

/**
 * 表单验证规则
 */
const ValidationRules = {
  required: { required: true, message: '此项为必填项', trigger: 'blur' },
  email: { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
  phone: { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  
  /**
   * 自定义长度验证
   * @param {number} min 最小长度
   * @param {number} max 最大长度
   * @returns {object} 验证规则
   */
  length(min, max) {
    return { min, max, message: `长度在 ${min} 到 ${max} 个字符`, trigger: 'blur' };
  },

  /**
   * 自定义数字范围验证
   * @param {number} min 最小值
   * @param {number} max 最大值
   * @returns {object} 验证规则
   */
  numberRange(min, max) {
    return { 
      type: 'number', 
      min, 
      max, 
      message: `请输入 ${min} 到 ${max} 之间的数字`, 
      trigger: 'blur' 
    };
  }
};

// ==================== 全局过滤器 ====================
Vue.filter('formatSize', formatFileSize);
Vue.filter('formatTime', formatTime);
Vue.filter('truncate', truncateText);

// ==================== 全局混入 ====================
Vue.mixin({
  methods: {
    $showSuccess: showSuccess,
    $showError: showError,
    $showWarning: showWarning,
    $showInfo: showInfo,
    $confirm: confirmDialog,
    $api: ApiClient,
    $storage: StorageManager
  }
});

// 导出到全局
window.CommonUtils = {
  formatFileSize,
  formatTime,
  truncateText,
  showSuccess,
  showError,
  showWarning,
  showInfo,
  confirmDialog,
  LoadingManager,
  ApiClient,
  StorageManager,
  ValidationRules
};
