# 设计文档

## 概述

本设计文档描述了如何将现有的Vue.js + Element UI前端界面完全迁移到Bootstrap UI框架。迁移将保持所有现有功能不变，同时移除Vue.js依赖，使用原生JavaScript和Bootstrap组件实现相同的用户体验。

## 架构

### 当前架构
- **前端框架**: Vue.js 2.x
- **UI组件库**: Element UI
- **状态管理**: Vue实例数据
- **事件处理**: Vue事件系统
- **模板渲染**: Vue模板语法

### 目标架构
- **前端框架**: 原生JavaScript (Vanilla JS)
- **UI组件库**: Bootstrap 5.x
- **状态管理**: JavaScript对象和DOM状态
- **事件处理**: 原生DOM事件
- **模板渲染**: JavaScript模板字符串和DOM操作

## 组件和接口

### 1. 布局组件迁移

#### 1.1 容器布局
**当前实现**:
```html
<el-container class="main-container">
  <el-main style="padding:0">
```

**目标实现**:
```html
<div class="main-container d-flex">
  <div class="main-content flex-grow-1">
```

#### 1.2 侧边栏组件
- **状态**: 已完成Bootstrap迁移
- **功能**: 导航菜单、系统状态显示
- **交互**: 原生JavaScript事件处理

#### 1.3 工具栏组件
- **状态**: 已完成Bootstrap迁移
- **功能**: 搜索、筛选、操作按钮
- **交互**: 原生JavaScript事件处理

### 2. 表单组件迁移

#### 2.1 选择器组件
**当前实现**:
```html
<el-select v-model="selectedDataset" @change="loadCollections">
  <el-option v-for="dataset in datasets" :key="dataset.id">
```

**目标实现**:
```html
<select class="form-select" onchange="handleDatasetChange(this.value)">
  <!-- 选项通过JavaScript动态生成 -->
</select>
```

#### 2.2 输入框组件
**当前实现**:
```html
<el-input v-model="editForm.q" type="textarea" :rows="6">
```

**目标实现**:
```html
<textarea class="form-control" rows="6" placeholder="请输入问题内容">
```

#### 2.3 按钮组件
**当前实现**:
```html
<el-button type="primary" @click="loadData" :loading="loading">
```

**目标实现**:
```html
<button class="btn btn-primary" onclick="loadData()">
  <span class="spinner-border spinner-border-sm d-none"></span>
  查询
</button>
```

### 3. 对话框组件迁移

#### 3.1 模态对话框
**当前实现**:
```html
<el-dialog :visible.sync="editDialogVisible" width="80%">
```

**目标实现**:
```html
<div class="modal fade" id="editModal" tabindex="-1">
  <div class="modal-dialog modal-xl">
```

#### 3.2 对话框状态管理
- **Vue方式**: 响应式数据绑定
- **Bootstrap方式**: JavaScript控制显示/隐藏

### 4. 数据展示组件迁移

#### 4.1 卡片组件
**当前实现**:
```html
<el-card class="audit-card">
  <div v-for="(item, index) in auditDataList" :key="index">
```

**目标实现**:
```html
<div class="card audit-card">
  <!-- 通过JavaScript动态生成卡片内容 -->
</div>
```

#### 4.2 表格组件
- **状态**: 已完成Bootstrap迁移（chat-logs.html）
- **功能**: 数据展示、分页、排序
- **交互**: 原生JavaScript处理

### 5. 文件上传组件迁移

#### 5.1 上传组件
**当前实现**:
```html
<el-upload :auto-upload="false" :on-change="handleImageChange">
```

**目标实现**:
```html
<div class="upload-area" ondrop="handleFileDrop(event)">
  <input type="file" onchange="handleFileSelect(event)">
</div>
```

## 数据模型

### 1. 状态管理模式

#### Vue响应式数据
```javascript
data() {
  return {
    auditDataList: [],
    selectedItems: [],
    loading: false
  }
}
```

#### JavaScript对象状态
```javascript
const AppState = {
  auditDataList: [],
  selectedItems: [],
  loading: false,
  
  // 状态更新方法
  updateAuditData(data) {
    this.auditDataList = data;
    this.renderAuditList();
  },
  
  setLoading(status) {
    this.loading = status;
    this.updateLoadingUI();
  }
};
```

### 2. 数据绑定模式

#### Vue双向绑定
```html
<el-input v-model="editForm.q">
```

#### 原生JavaScript绑定
```javascript
function updateFormData() {
  const formData = {
    q: document.getElementById('questionInput').value,
    a: document.getElementById('answerInput').value
  };
  return formData;
}

function populateForm(data) {
  document.getElementById('questionInput').value = data.q || '';
  document.getElementById('answerInput').value = data.a || '';
}
```

## 错误处理

### 1. 表单验证
- **Bootstrap验证类**: `is-valid`, `is-invalid`
- **自定义验证函数**: JavaScript表单验证
- **错误消息显示**: Bootstrap反馈组件

### 2. API错误处理
- **统一错误处理函数**
- **用户友好的错误提示**
- **网络错误重试机制**

### 3. 兼容性处理
- **浏览器兼容性检查**
- **Polyfill支持**
- **优雅降级策略**

## 测试策略

### 1. 功能测试
- **页面加载测试**: 确保所有页面正常加载
- **交互测试**: 验证所有用户交互功能
- **数据流测试**: 确保数据正确传递和显示

### 2. 兼容性测试
- **浏览器兼容性**: Chrome, Firefox, Safari, Edge
- **设备兼容性**: 桌面、平板、手机
- **响应式测试**: 不同屏幕尺寸适配

### 3. 性能测试
- **加载性能**: 页面加载时间优化
- **运行性能**: JavaScript执行效率
- **内存使用**: 避免内存泄漏

## 迁移策略

### 1. 分阶段迁移
1. **第一阶段**: 迁移audit.html和image-management.html
2. **第二阶段**: 更新主入口文件frontend/index.html
3. **第三阶段**: 清理Vue.js相关依赖和代码

### 2. 向后兼容
- **API接口保持不变**
- **数据格式保持一致**
- **URL路由保持相同**

### 3. 渐进式替换
- **保留现有功能**
- **逐步替换组件**
- **确保每个阶段都可独立运行**

## 技术细节

### 1. JavaScript模块化
```javascript
// 状态管理模块
const StateManager = {
  state: {},
  setState(newState) { /* 更新状态 */ },
  getState() { /* 获取状态 */ }
};

// API调用模块
const ApiService = {
  get(url) { /* GET请求 */ },
  post(url, data) { /* POST请求 */ }
};

// UI更新模块
const UIRenderer = {
  renderList(data) { /* 渲染列表 */ },
  showModal(id) { /* 显示模态框 */ }
};
```

### 2. 事件处理系统
```javascript
// 事件委托处理
document.addEventListener('click', function(e) {
  if (e.target.matches('.audit-btn')) {
    handleAudit(e.target);
  }
  if (e.target.matches('.delete-btn')) {
    handleDelete(e.target);
  }
});
```

### 3. 模板渲染系统
```javascript
function renderAuditCard(item) {
  return `
    <div class="card audit-card" data-id="${item.id}">
      <div class="card-body">
        <h5 class="card-title">ID: ${item.id}</h5>
        <div class="card-text">${item.processedQ}</div>
        <button class="btn btn-success audit-btn">通过</button>
        <button class="btn btn-danger reject-btn">拒绝</button>
      </div>
    </div>
  `;
}
```

这个设计确保了从Vue.js到Bootstrap的平滑迁移，保持了所有现有功能，同时提供了更好的性能和更少的依赖。