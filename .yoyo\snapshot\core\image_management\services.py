"""
图片管理服务层
处理图片相关的业务逻辑
"""

import re
import base64
from typing import Dict, List, Any, Tuple
from bson.objectid import ObjectId

from database.connection import get_database
from config.settings import settings


class ImageService:
    """图片管理服务类"""
    
    def __init__(self):
        self.pattern = r'!\[.*?\]\(/api/system/img/[a-f0-9]{24}\.(?:png|jpeg|jpg)\)'
        self.global_datasets_dic = {}
        self.global_datasets_collections_dic = {}
    
    def get_global_data(self):
        """获取全局数据"""
        db = get_database()
        datasets = db['datasets'].find({"_id": {"$in": settings.dataids_list}})
        self.global_datasets_dic = {str(d["_id"]): d["name"] for d in datasets}
        
        collections = db['dataset_collections'].find({
            "datasetId": {"$in": settings.dataids_list}, 
            "type": "file"
        })
        self.global_datasets_collections_dic = {str(d["_id"]): d["name"] for d in collections}
    
    def get_collections_list(self, dataset_id: str = None) -> Dict[str, Any]:
        """获取集合列表"""
        try:
            if dataset_id:
                dataset_id = [ObjectId(dataset_id)]
            else:
                dataset_id = [settings.dataids_list[0]]
            
            db = get_database()
            dataset_collections = db['dataset_collections'].find({
                "datasetId": {"$in": dataset_id}, 
                "type": "file"
            })
            datasets_dic = [{"id": str(d["_id"]), "name": d["name"]} for d in dataset_collections]
            return {"code": 200, "data": datasets_dic}
        except Exception as e:
            return {"code": 500, "message": str(e)}
    
    def get_image_data(self, q: str, tag: int = 0) -> Tuple[Dict, List]:
        """提取图片数据"""
        match_dict = {}
        picid_list = []
        
        if '/api/system/img/' in q:
            matches = re.findall(self.pattern, q)
            for matche in matches:
                picid = matche.split('img/')[1].split('.')[0]
                if tag == 0:
                    new_matche = matche.split('(')[1].split(')')[0]
                    updateimage = f'<img src="" style="width: 100px; height: auto;" class="uploadedImage">'
                    match_dict[matche] = {
                        "src": f'<img src="http://{settings.api_ip}:3000{new_matche}" loading="lazy" referrerpolicy="no-referrer"/>',
                        "picid": picid,
                        "updateimage": updateimage
                    }
                picid_list.append(ObjectId(picid))
        
        if tag == 0:
            return match_dict, picid_list
        else:
            return picid_list
    
    def get_datasets_datas_images(self, collection_id: str, date_start: str = None, 
                                 date_end: str = None, offset: int = 0, 
                                 limit: int = 10, is_audit: int = 0) -> Dict[str, Any]:
        """获取数据集图片数据"""
        try:
            if not self.global_datasets_dic or not self.global_datasets_collections_dic:
                self.get_global_data()
            
            db = get_database()
            query = {"collectionId": ObjectId(collection_id)}
            
            # 添加日期过滤
            if date_start and date_end:
                # 这里可以添加日期过滤逻辑
                pass
            
            # 添加审核状态过滤
            if is_audit:
                # 这里可以添加审核状态过滤逻辑
                pass
            
            dataset_datas = db['dataset_datas'].find(query).skip(offset).limit(limit)
            total = db['dataset_datas'].count_documents(query)
            
            dataset_list = []
            for dd in dataset_datas:
                q = dd["q"]
                images_dict, picid_list = self.get_image_data(dd["q"])
                
                # 替换图片标记为HTML
                for k, v in images_dict.items():
                    q = q.replace(k, v["src"])
                
                # 获取图片上传信息
                uploadimages = db['images'].find({"_id": {"$in": picid_list}})
                uploadimages_str = ""
                audit = 0
                images_dict_keys = images_dict.keys()
                
                for upimage in uploadimages:
                    metadata = upimage.get("metadata", {})
                    if not metadata:
                        continue
                    
                    if metadata.get("audit", "") == "1":
                        audit = 1
                    
                    uploadTag = upimage["metadata"].get("uploadTag", "")
                    if uploadTag in ["0", "1"]:
                        mime = metadata.get('mime', '')
                        kkk = None
                        for kkkey in images_dict_keys:
                            if str(upimage["_id"]) in kkkey:
                                kkk = kkkey
                                break
                        
                        if uploadTag == "0":
                            imagedata = upimage["metadata"].get("imagedata", "")
                            imagedata_base64 = ""
                            if imagedata:
                                imagedata_base64 = base64.b64encode(imagedata).decode('utf-8')
                            uploadimages_str += f'<img src="data:{mime};base64,{imagedata_base64}" style="width: 100px; height: auto;" class="uploadedImage">'
                        else:
                            uploadimages_str += f'<span style="color: red;">删除图片</span>'
                
                dataset_name = self.global_datasets_dic.get(str(dd.get("datasetId", "")), "")
                collection_name = self.global_datasets_collections_dic.get(str(dd.get("collectionId", "")), "")
                
                dataset_list.append([
                    str(dd["_id"]),
                    dd.get("updateTime", ""),
                    dataset_name,
                    collection_name,
                    q,
                    uploadimages_str,
                    audit
                ])
            
            return {
                "code": 200,
                "data": {
                    "total": total,
                    "data": dataset_list
                }
            }
        except Exception as e:
            return {"code": 500, "message": str(e)}
    
    def upload_image(self, body: Dict[str, Any]) -> Dict[str, Any]:
        """上传图片"""
        try:
            db = get_database()
            type_val = body.get("type", "0")
            
            if type_val in ["4", "3"]:
                # 直接对知识片段进行操作  4 删除片段里面的图片   3 还原片段里面的操作
                picidobj = [ObjectId(bb) for bb in body["picid"]]
                picobjs = db['images'].find({"_id": {"$in": picidobj}})
                
                for picobj in picobjs:
                    metadata = picobj["metadata"]
                    if type_val == "4":
                        if picobj.get("metadata", {}).get("uploadTag", "") != "1":
                            metadata["uploadTag"] = "1"
                            metadata["audit"] = "0"
                            metadata["datasets_dataid"] = body["datasets_dataid"]
                    else:
                        metadata = {"mime": metadata["mime"], "relatedId": metadata['relatedId']}
                    
                    update = {"$set": {"metadata": metadata}}
                    db['images'].update_one({"_id": picobj["_id"]}, update)
                
                return {"code": 200}
            else:
                query = {"_id": ObjectId(body["picid"])}
                metadata = list(db["images"].find(query))[0].get("metadata", {})
                
                if type_val == "0":
                    imagedata = base64.b64decode(body["fix"].split(',')[1])
                    metadata["uploadTag"] = "0"
                    metadata["imagedata"] = imagedata
                    metadata["audit"] = "0"
                    metadata["datasets_dataid"] = body["datasets_dataid"]
                elif type_val == "1":
                    metadata["uploadTag"] = "1"
                    metadata["datasets_dataid"] = body["datasets_dataid"]
                    metadata["audit"] = "0"
                else:
                    # 2 是清除提交内容
                    metadata = {"mime": metadata["mime"], "relatedId": metadata['relatedId']}
                
                update = {"$set": {"metadata": metadata}}
                dataset_datas = db['images'].update_one(query, update)
                
                if dataset_datas.modified_count > 0:
                    return {"code": 200}
                else:
                    return {"code": 500, "message": "更新失败"}
        except Exception as e:
            return {"code": 500, "message": str(e)}
    
    def audit_image(self, collection_id: str) -> Dict[str, Any]:
        """审核图片"""
        try:
            db = get_database()
            query = {"metadata.audit": "0"}
            imagesdatas = db['images'].find(query)
            
            updatecount = 0
            delcount = 0
            
            for imdata in imagesdatas:
                try:
                    metadata = imdata["metadata"]
                    # uploadTag 0 是更新图片  1是删除图片
                    if metadata["uploadTag"] == "0":
                        imagedata = metadata.get("imagedata", imdata["binary"])
                        metadata["audit"] = "1"
                        update = {"$set": {"metadata": metadata, "binary": imagedata}}
                        update_res = db['images'].update_one({"_id": imdata["_id"]}, update)
                        if update_res.modified_count > 0:
                            updatecount += 1
                    else:
                        mime = metadata.get("mime", "")
                        datasets_dataid = metadata["datasets_dataid"]
                        image_pattern = re.compile(r'!\[.*?\]\(/api/system/img/' + str(imdata["_id"]) + r'\.' + mime.replace('image/', '') + r'\)')
                        
                        deldatas = db["dataset_datas"].find({"_id": ObjectId(datasets_dataid)})
                        update = {}
                        for delda in deldatas:
                            q = image_pattern.sub('', delda["q"])
                            update = {"$set": {"q": q}}
                        
                        if not update:
                            continue
                        
                        update_res = db['dataset_datas'].update_one({"_id": ObjectId(datasets_dataid)}, update)
                        del_res = db['images'].delete_one({"_id": imdata["_id"]})
                        if del_res.deleted_count > 0:
                            delcount += 1
                except Exception as e:
                    print('图片审核错误:', str(e))
            
            return {"code": 200, "msg": f"更新{updatecount}条数据，删除{delcount}条数据"}
        except Exception as e:
            return {"code": 500, "message": str(e)}


# 创建服务实例
image_service = ImageService()
