"""
知识库数据验证模式
定义API请求和响应的数据验证
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class DatasetResponse(BaseModel):
    """数据集响应模式"""
    id: str
    name: str


class CollectionListRequest(BaseModel):
    """集合列表请求模式"""
    keyword: Optional[str] = ""
    start_date: Optional[str] = ""
    end_date: Optional[str] = ""
    status: Optional[str] = ""
    dataset: Optional[str] = ""


class CollectionResponse(BaseModel):
    """集合响应模式"""
    id: str
    dataset_id: str
    dataset_name: str
    name: str
    time: str
    audit: str
    cpxh: Optional[str] = ""
    size: int


class DataUpdateRequest(BaseModel):
    """数据更新请求模式"""
    data: List[Any] = Field(..., description="包含[id, q, a, indexes]的数组")


class AuditRequest(BaseModel):
    """审核请求模式"""
    collection_id: str
    token: str


class LoginRequest(BaseModel):
    """登录请求模式"""
    username: str
    password: str


class LoginResponse(BaseModel):
    """登录响应模式"""
    code: int
    message: str
    token: Optional[str] = None


class ApiResponse(BaseModel):
    """通用API响应模式"""
    code: int
    data: Optional[Any] = None
    message: Optional[str] = None
    server_ip: Optional[str] = None
