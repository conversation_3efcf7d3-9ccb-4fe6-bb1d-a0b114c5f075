/* 对话日志管理 - 专用样式 */

/* ==================== 表格容器样式 ==================== */
.table-container {
  padding: 20px;
  background: #fff;
  margin: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* ==================== 详情对话框样式 ==================== */
.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.chat-content {
  margin-top: 20px;
}

.user-message,
.assistant-message,
.business-info {
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #409EFF;
  background: #f8f9fa;
}

.assistant-message {
  border-left-color: #67C23A;
}

.business-info {
  border-left-color: #E6A23C;
}

.message-label {
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  font-size: 14px;
}

.message-content {
  color: #606266;
  line-height: 1.6;
  word-break: break-word;
}

/* ==================== 表格样式增强 ==================== */
.el-table .cell {
  word-break: break-word;
}

.el-table__row:hover {
  background-color: #f5f7fa;
}

/* ==================== 工具栏样式 ==================== */
.toolbar {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #fff;
  border-bottom: 1px solid #ebeef5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* ==================== 分页样式 ==================== */
.el-pagination {
  padding: 20px 0;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .toolbar .el-select,
  .toolbar .el-date-picker {
    width: 100% !important;
    margin-right: 0 !important;
    margin-bottom: 10px;
  }
  
  .table-container {
    margin: 10px;
    padding: 10px;
  }
}
