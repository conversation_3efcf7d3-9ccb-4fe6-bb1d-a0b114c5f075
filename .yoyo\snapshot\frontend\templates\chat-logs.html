{% extends "base.html" %}

{% block title %}对话日志管理{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/frontend/static/css/chat-logs.css">
{% endblock %}

{% block content %}
<el-container class="main-container">
  <!-- 侧边栏导航 -->
  {% include 'components/layout/sidebar.html' %}

  <!-- 右侧内容 -->
  <el-main style="padding:0">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <el-select v-model="selectedApp" placeholder="选择应用" style="width: 200px; margin-right: 10px;">
        <el-option
          v-for="app in appList"
          :key="app.id"
          :label="app.name"
          :value="app.id">
        </el-option>
      </el-select>

      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        style="margin-right: 10px;">
      </el-date-picker>

      <el-button type="primary" @click="queryLogs" :loading="loading">查询</el-button>
      <el-button type="success" @click="exportData" :loading="exporting">导出</el-button>
    </div>
    
    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        style="width: 100%"
        height="calc(100vh - 200px)"
        v-loading="loading">
        
        <el-table-column prop="id" label="对话ID" width="200"></el-table-column>
        <el-table-column prop="time" label="时间" width="180" :formatter="formatTime"></el-table-column>
        <el-table-column prop="userChatInput" label="用户输入" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="assistantChatOutput" label="助手回复" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="ywk" label="业务信息" min-width="150" show-overflow-tooltip></el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" @click="viewDetail(scope.row)">详情</el-button>
            <el-button type="text" @click="saveFeedback(scope.row)">反馈</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        layout="total, prev, pager, next, jumper"
        style="margin-top: 20px; text-align: center;">
      </el-pagination>
    </div>
  </el-main>
</el-container>

<!-- 详情对话框 -->
<el-dialog
  title="对话详情"
  :visible.sync="detailDialogVisible"
  width="80%"
  top="5vh">
  
  <div class="detail-content">
    <el-descriptions title="基本信息" :column="2" border>
      <el-descriptions-item label="对话ID">{{ currentDetail.id }}</el-descriptions-item>
      <el-descriptions-item label="时间">{{ formatTime(null, null, currentDetail.time) }}</el-descriptions-item>
      <el-descriptions-item label="应用">{{ getAppName(currentDetail.appId) }}</el-descriptions-item>
      <el-descriptions-item label="用户">{{ currentDetail.userId || '匿名' }}</el-descriptions-item>
    </el-descriptions>
    
    <el-divider>对话内容</el-divider>
    
    <div class="chat-content">
      <div class="user-message">
        <div class="message-label">用户输入：</div>
        <div class="message-content" v-html="currentDetail.userChatInput"></div>
      </div>
      
      <div class="assistant-message">
        <div class="message-label">助手回复：</div>
        <div class="message-content" v-html="currentDetail.assistantChatOutput"></div>
      </div>
      
      <div v-if="currentDetail.ywk" class="business-info">
        <div class="message-label">业务信息：</div>
        <div class="message-content">{{ currentDetail.ywk }}</div>
      </div>
    </div>
  </div>
  
  <div slot="footer" class="dialog-footer">
    <el-button @click="detailDialogVisible = false">关闭</el-button>
  </div>
</el-dialog>

<!-- 反馈对话框 -->
<el-dialog
  title="保存反馈"
  :visible.sync="feedbackDialogVisible"
  width="600px">
  
  <el-form :model="feedbackForm" label-width="100px">
    <el-form-item label="对话ID">
      <el-input v-model="feedbackForm.chatId" readonly></el-input>
    </el-form-item>
    
    <el-form-item label="反馈类型">
      <el-select v-model="feedbackForm.type" placeholder="请选择反馈类型" style="width: 100%;">
        <el-option label="满意" value="satisfied"></el-option>
        <el-option label="不满意" value="unsatisfied"></el-option>
        <el-option label="错误回复" value="error"></el-option>
        <el-option label="其他" value="other"></el-option>
      </el-select>
    </el-form-item>
    
    <el-form-item label="反馈内容">
      <el-input
        type="textarea"
        v-model="feedbackForm.content"
        :rows="4"
        placeholder="请输入反馈内容">
      </el-input>
    </el-form-item>
  </el-form>
  
  <div slot="footer" class="dialog-footer">
    <el-button @click="feedbackDialogVisible = false">取消</el-button>
    <el-button type="primary" @click="submitFeedback" :loading="submitting">提交</el-button>
  </div>
</el-dialog>
{% endblock %}

{% block extra_js %}
<script src="/frontend/static/js/chat-logs.js"></script>
{% endblock %}
