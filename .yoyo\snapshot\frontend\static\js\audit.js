/**
 * 内容审核 - JavaScript
 */

// 审核管理应用组件
const AuditApp = {
  init() {
    new Vue({
      el: '#app',
      data() {
        return {
          // 基础数据
          datasets: [],
          collections: [],
          selectedDataset: '',
          selectedCollection: '',
          loading: false,
          
          // 审核数据
          auditDataList: [],
          selectedItems: [],
          selectAll: false,
          batchAuditing: false,
          
          // 编辑对话框
          editDialogVisible: false,
          editForm: {
            id: '',
            q: '',
            a: '',
            auditNote: ''
          },
          saving: false,
          
          // 图片预览
          previewDialogVisible: false,
          previewImageSrc: '',
          
          // 拒绝对话框
          rejectDialogVisible: false,
          rejectForm: {
            reason: '',
            note: ''
          },
          rejecting: false,
          currentRejectItem: null,
          
          // 侧边栏状态
          activeMenu: '4',
          sfilesh: true // 审核页面需要权限
        };
      },
      
      mounted() {
        this.initializeApp();
      },
      
      methods: {
        /**
         * 初始化应用
         */
        async initializeApp() {
          await this.loadDatasets();
          if (this.datasets.length > 0) {
            this.selectedDataset = this.datasets[0].id;
            await this.loadCollections();
          }
        },
        
        /**
         * 加载数据集列表
         */
        async loadDatasets() {
          try {
            const response = await this.$api.get('/getDatasList/');
            if (response.code === 200) {
              this.datasets = response.data;
            }
          } catch (error) {
            console.error('加载数据集失败:', error);
            this.$showError('加载数据集失败');
          }
        },
        
        /**
         * 加载集合列表
         */
        async loadCollections() {
          if (!this.selectedDataset) return;
          
          try {
            const response = await this.$api.get('/getcollectionsList/', {
              datasetId: this.selectedDataset
            });
            if (response.code === 200) {
              this.collections = response.data;
              if (this.collections.length > 0) {
                this.selectedCollection = this.collections[0].id;
                await this.loadAuditData();
              }
            }
          } catch (error) {
            console.error('加载集合列表失败:', error);
            this.$showError('加载集合列表失败');
          }
        },
        
        /**
         * 加载审核数据
         */
        async loadAuditData() {
          if (!this.selectedCollection) return;
          
          this.loading = true;
          try {
            const response = await this.$api.get('/getDatasetsDatas_images/', {
              collectionId: this.selectedCollection
            });
            
            if (response.code === 200) {
              this.auditDataList = response.data.map(item => ({
                id: item[0],
                q: item[1],
                a: item[2] || '',
                processedQ: this.processImageContent(item[1]),
                images: this.extractImages(item[1]),
                metadata: this.extractMetadata(item),
                selected: false,
                approving: false,
                rejecting: false
              }));
            }
          } catch (error) {
            console.error('加载审核数据失败:', error);
            this.$showError('加载审核数据失败');
          } finally {
            this.loading = false;
          }
        },
        
        /**
         * 处理图片内容
         */
        processImageContent(content) {
          if (!content) return '';
          
          const imagePattern = /!\[([^\]]*)\]\(([^)]+)\)/g;
          return content.replace(imagePattern, (match, alt, src) => {
            if (src.includes('/api/system/img/')) {
              return `<img src="http://171.43.138.237:3000${src}" alt="${alt}" style="max-width: 200px; height: auto; margin: 5px;" loading="lazy" referrerpolicy="no-referrer"/>`;
            }
            return match;
          });
        },
        
        /**
         * 提取图片信息
         */
        extractImages(content) {
          if (!content) return [];
          
          const images = [];
          const imagePattern = /!\[([^\]]*)\]\(([^)]+)\)/g;
          let match;
          
          while ((match = imagePattern.exec(content)) !== null) {
            const [fullMatch, alt, src] = match;
            if (src.includes('/api/system/img/')) {
              images.push({
                alt: alt,
                src: `http://171.43.138.237:3000${src}`,
                originalMatch: fullMatch
              });
            }
          }
          
          return images;
        },
        
        /**
         * 提取元数据
         */
        extractMetadata(item) {
          // 这里可以根据实际数据结构提取元数据
          return {
            '数据长度': item[1] ? item[1].length : 0,
            '包含图片': this.extractImages(item[1]).length,
            '创建时间': new Date().toLocaleString()
          };
        },
        
        /**
         * 全选处理
         */
        handleSelectAll(value) {
          this.auditDataList.forEach(item => {
            item.selected = value;
          });
          this.updateSelectedItems();
        },
        
        /**
         * 单项选择处理
         */
        handleItemSelect() {
          this.updateSelectedItems();
          this.selectAll = this.selectedItems.length === this.auditDataList.length;
        },
        
        /**
         * 更新选中项目
         */
        updateSelectedItems() {
          this.selectedItems = this.auditDataList.filter(item => item.selected);
        },
        
        /**
         * 批量审核
         */
        async batchAudit() {
          if (this.selectedItems.length === 0) {
            this.$showWarning('请先选择要审核的项目');
            return;
          }
          
          try {
            await this.$confirm(`确定要批量通过 ${this.selectedItems.length} 个项目吗？`, '批量审核');
            
            this.batchAuditing = true;
            
            for (const item of this.selectedItems) {
              try {
                await this.approveItemInternal(item);
              } catch (error) {
                console.error(`审核项目 ${item.id} 失败:`, error);
              }
            }
            
            this.$showSuccess('批量审核完成');
            await this.loadAuditData();
            
          } catch (error) {
            if (error !== 'cancel') {
              console.error('批量审核失败:', error);
              this.$showError('批量审核失败');
            }
          } finally {
            this.batchAuditing = false;
          }
        },
        
        /**
         * 通过审核
         */
        async approveItem(item) {
          item.approving = true;
          try {
            await this.approveItemInternal(item);
            this.$showSuccess('审核通过');
            await this.loadAuditData();
          } catch (error) {
            console.error('审核失败:', error);
            this.$showError('审核失败');
          } finally {
            item.approving = false;
          }
        },
        
        /**
         * 内部审核通过方法
         */
        async approveItemInternal(item) {
          const response = await this.$api.post('/whauditimage/', {
            collectionId: this.selectedCollection,
            dataId: item.id,
            action: 'approve'
          });
          
          if (response.code !== 200) {
            throw new Error(response.message || '审核失败');
          }
        },
        
        /**
         * 拒绝审核
         */
        rejectItem(item) {
          this.currentRejectItem = item;
          this.rejectForm = {
            reason: '',
            note: ''
          };
          this.rejectDialogVisible = true;
        },
        
        /**
         * 确认拒绝
         */
        async confirmReject() {
          if (!this.rejectForm.reason) {
            this.$showWarning('请选择拒绝原因');
            return;
          }
          
          this.rejecting = true;
          try {
            const response = await this.$api.post('/whauditimage/', {
              collectionId: this.selectedCollection,
              dataId: this.currentRejectItem.id,
              action: 'reject',
              reason: this.rejectForm.reason,
              note: this.rejectForm.note
            });
            
            if (response.code === 200) {
              this.$showSuccess('已拒绝该项目');
              this.rejectDialogVisible = false;
              await this.loadAuditData();
            } else {
              this.$showError('拒绝失败');
            }
          } catch (error) {
            console.error('拒绝失败:', error);
            this.$showError('拒绝失败');
          } finally {
            this.rejecting = false;
          }
        },
        
        /**
         * 编辑项目
         */
        editItem(item) {
          this.editForm = {
            id: item.id,
            q: item.q,
            a: item.a,
            auditNote: ''
          };
          this.editDialogVisible = true;
        },
        
        /**
         * 保存编辑
         */
        async saveEdit() {
          this.saving = true;
          try {
            const response = await this.$api.post('/updateDatasetdatas/', {
              data: [this.editForm.id, this.editForm.q, this.editForm.a, []]
            });
            
            if (response.code === 200) {
              this.$showSuccess('保存成功');
              this.editDialogVisible = false;
              await this.loadAuditData();
            } else {
              this.$showError('保存失败');
            }
          } catch (error) {
            console.error('保存失败:', error);
            this.$showError('保存失败');
          } finally {
            this.saving = false;
          }
        },
        
        /**
         * 预览图片
         */
        previewImage(image) {
          this.previewImageSrc = image.src;
          this.previewDialogVisible = true;
        },
        
        /**
         * 处理菜单选择
         */
        handleMenuSelect(index) {
          if (index === '2') {
            window.router.navigate('knowledge-base');
          } else if (index === '1') {
            window.router.navigate('chat-logs');
          } else if (index === '3') {
            window.router.navigate('image-management');
          } else if (index === '4') {
            this.activeMenu = index;
          }
        }
      }
    });
  }
};

// 导出到全局
window.AuditApp = AuditApp;
