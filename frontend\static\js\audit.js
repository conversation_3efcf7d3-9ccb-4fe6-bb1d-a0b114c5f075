/**
 * 内容审核 - JavaScript (原生实现)
 */

// 审核管理器
const AuditManager = {
  // 应用状态
  state: {
    // 基础数据
    datasets: [],
    collections: [],
    selectedDataset: '',
    selectedCollection: '',
    loading: false,

    // 审核数据
    auditDataList: [],
    selectedItems: [],
    selectAll: false,
    batchAuditing: false,

    // 编辑对话框
    editForm: {
      id: '',
      q: '',
      a: '',
      auditNote: ''
    },
    saving: false,

    // 图片预览
    previewImageSrc: '',

    // 拒绝对话框
    rejectForm: {
      reason: '',
      note: ''
    },
    rejecting: false,
    currentRejectItem: null,

    // 侧边栏状态
    activeMenu: '4',
    sfilesh: true // 审核页面需要权限
  },

  // 初始化应用
  async init() {
    await this.initializeApp();
    this.bindEvents();
  },

  // 绑定事件
  bindEvents() {
    // 绑定全选复选框事件
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
      selectAllCheckbox.addEventListener('change', (e) => {
        this.handleSelectAll(e.target.checked);
      });
    }
  },

  /**
   * 初始化应用
   */
  async initializeApp() {
    await this.loadDatasets();
    if (this.state.datasets.length > 0) {
      this.state.selectedDataset = this.state.datasets[0].id;
      await this.loadCollections();
    }
  },

  /**
   * 加载数据集列表
   */
  async loadDatasets() {
    try {
      const response = await ApiClient.get('/getDatasList/');
      if (response.code === 200) {
        this.state.datasets = response.data;
        this.updateDatasetSelect();
      }
    } catch (error) {
      console.error('加载数据集失败:', error);
      showError('加载数据集失败');
    }
  },

  /**
   * 更新数据集下拉框
   */
  updateDatasetSelect() {
    const select = document.getElementById('selectedDataset');
    if (!select) return;

    // 清空现有选项
    select.innerHTML = '<option value="">选择数据集</option>';

    // 添加数据集选项
    this.state.datasets.forEach(dataset => {
      const option = document.createElement('option');
      option.value = dataset.id;
      option.textContent = dataset.name;
      select.appendChild(option);
    });
  },

  /**
   * 加载集合列表
   */
  async loadCollections() {
    const selectedDataset = document.getElementById('selectedDataset').value;
    if (!selectedDataset) return;

    this.state.selectedDataset = selectedDataset;

    try {
      const response = await ApiClient.get('/getcollectionsList/', {
        datasetId: selectedDataset
      });
      if (response.code === 200) {
        this.state.collections = response.data;
        this.updateCollectionSelect();
        if (this.state.collections.length > 0) {
          this.state.selectedCollection = this.state.collections[0].id;
          document.getElementById('selectedCollection').value = this.state.selectedCollection;
          await this.loadAuditData();
        }
      }
    } catch (error) {
      console.error('加载集合列表失败:', error);
      showError('加载集合列表失败');
    }
  },

  /**
   * 更新集合下拉框
   */
  updateCollectionSelect() {
    const select = document.getElementById('selectedCollection');
    if (!select) return;

    // 清空现有选项
    select.innerHTML = '<option value="">选择集合</option>';

    // 添加集合选项
    this.state.collections.forEach(collection => {
      const option = document.createElement('option');
      option.value = collection.id;
      option.textContent = collection.name;
      select.appendChild(option);
    });
  },

  /**
   * 加载审核数据
   */
  async loadAuditData() {
    const selectedCollection = document.getElementById('selectedCollection').value;
    if (!selectedCollection) return;

    this.state.selectedCollection = selectedCollection;
    this.state.loading = true;
    this.showLoading(true);

    try {
      const response = await ApiClient.get('/getDatasetsDatas_images/', {
        collectionId: selectedCollection
      });

      if (response.code === 200) {
        this.state.auditDataList = response.data.map(item => ({
          id: item[0],
          q: item[1],
          a: item[2] || '',
          processedQ: this.processImageContent(item[1]),
          images: this.extractImages(item[1]),
          metadata: this.extractMetadata(item),
          selected: false,
          approving: false,
          rejecting: false
        }));

        this.renderAuditData();
      }
    } catch (error) {
      console.error('加载审核数据失败:', error);
      showError('加载审核数据失败');
    } finally {
      this.state.loading = false;
      this.showLoading(false);
    }
  },

  /**
   * 显示/隐藏加载状态
   */
  showLoading(show) {
    const spinner = document.getElementById('querySpinner');
    const btn = document.getElementById('queryBtn');

    if (spinner && btn) {
      if (show) {
        spinner.classList.remove('d-none');
        btn.disabled = true;
      } else {
        spinner.classList.add('d-none');
        btn.disabled = false;
      }
    }
  },

  /**
   * 渲染审核数据
   */
  renderAuditData() {
    const container = document.getElementById('auditDataList');
    const emptyState = document.getElementById('emptyState');
    const auditDataContainer = document.getElementById('auditDataContainer');

    if (this.state.auditDataList.length === 0) {
      emptyState.classList.remove('d-none');
      auditDataContainer.classList.add('d-none');
      return;
    }

    emptyState.classList.add('d-none');
    auditDataContainer.classList.remove('d-none');

    // 清空容器
    container.innerHTML = '';

    // 渲染每个审核项
    this.state.auditDataList.forEach((item, index) => {
      const itemHtml = this.createAuditItemHtml(item, index);
      container.insertAdjacentHTML('beforeend', itemHtml);
    });

    this.updateSelectedCount();
  },

  /**
   * 创建审核项HTML
   */
  createAuditItemHtml(item, index) {
    const imagesHtml = item.images.length > 0 ? `
      <div class="images-section mt-3">
        <h6>相关图片：</h6>
        <div class="images-grid d-flex flex-wrap gap-2">
          ${item.images.map((image, imgIndex) => `
            <div class="image-item">
              <img src="${image.src}"
                   onclick="AuditManager.previewImage('${image.src}')"
                   class="thumbnail img-thumbnail"
                   style="max-width: 150px; height: auto; cursor: pointer;">
            </div>
          `).join('')}
        </div>
      </div>
    ` : '';

    const metadataHtml = item.metadata ? `
      <div class="metadata-section mt-3">
        <h6>元数据信息：</h6>
        <div class="metadata-grid">
          ${Object.entries(item.metadata).map(([key, value]) => `
            <div class="metadata-item d-flex">
              <span class="metadata-key fw-bold me-2">${key}:</span>
              <span class="metadata-value">${value}</span>
            </div>
          `).join('')}
        </div>
      </div>
    ` : '';

    return `
      <div class="audit-data-item mb-3" data-index="${index}">
        <div class="card audit-card">
          <div class="card-header audit-header d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
              <div class="form-check me-3">
                <input class="form-check-input" type="checkbox"
                       id="item_${index}"
                       onchange="AuditManager.handleItemSelect(${index}, this.checked)">
              </div>
              <span class="data-id text-muted">ID: ${item.id}</span>
            </div>
            <div class="audit-actions">
              <button type="button" class="btn btn-success btn-sm me-1"
                      onclick="AuditManager.approveItem(${index})"
                      id="approveBtn_${index}">
                <span class="spinner-border spinner-border-sm d-none" id="approveSpinner_${index}"></span>
                通过
              </button>
              <button type="button" class="btn btn-danger btn-sm me-1"
                      onclick="AuditManager.rejectItem(${index})"
                      id="rejectBtn_${index}">
                <span class="spinner-border spinner-border-sm d-none" id="rejectSpinner_${index}"></span>
                拒绝
              </button>
              <button type="button" class="btn btn-outline-primary btn-sm"
                      onclick="AuditManager.editItem(${index})">
                <i class="bi bi-pencil"></i> 编辑
              </button>
            </div>
          </div>

          <div class="card-body audit-content">
            <div class="question-section mb-3">
              <h6>问题内容：</h6>
              <div class="content-display">${item.processedQ}</div>
            </div>

            ${item.a ? `
              <div class="answer-section mb-3">
                <h6>答案内容：</h6>
                <div class="content-display">${item.a}</div>
              </div>
            ` : ''}

            ${imagesHtml}
            ${metadataHtml}
          </div>
        </div>
      </div>
    `;
  },

  /**
   * 处理图片内容
   */
  processImageContent(content) {
    if (!content) return '';

    const imagePattern = /!\[([^\]]*)\]\(([^)]+)\)/g;
    return content.replace(imagePattern, (match, alt, src) => {
      if (src.includes('/api/system/img/')) {
        return `<img src="http://171.43.138.237:3000${src}" alt="${alt}" style="max-width: 200px; height: auto; margin: 5px;" loading="lazy" referrerpolicy="no-referrer"/>`;
      }
      return match;
    });
  },

  /**
   * 提取图片信息
   */
  extractImages(content) {
    if (!content) return [];

    const images = [];
    const imagePattern = /!\[([^\]]*)\]\(([^)]+)\)/g;
    let match;

    while ((match = imagePattern.exec(content)) !== null) {
      const [fullMatch, alt, src] = match;
      if (src.includes('/api/system/img/')) {
        images.push({
          alt: alt,
          src: `http://171.43.138.237:3000${src}`,
          originalMatch: fullMatch
        });
      }
    }

    return images;
  },

  /**
   * 提取元数据
   */
  extractMetadata(item) {
    // 这里可以根据实际数据结构提取元数据
    return {
      '数据长度': item[1] ? item[1].length : 0,
      '包含图片': this.extractImages(item[1]).length,
      '创建时间': new Date().toLocaleString()
    };
  },

  /**
   * 全选处理
   */
  handleSelectAll(checked) {
    this.state.selectAll = checked;
    this.state.auditDataList.forEach((item, index) => {
      item.selected = checked;
      const checkbox = document.getElementById(`item_${index}`);
      if (checkbox) {
        checkbox.checked = checked;
      }
    });
    this.updateSelectedItems();
  },

  /**
   * 单项选择处理
   */
  handleItemSelect(index, checked) {
    this.state.auditDataList[index].selected = checked;
    this.updateSelectedItems();

    // 更新全选状态
    const allSelected = this.state.auditDataList.every(item => item.selected);
    const noneSelected = this.state.auditDataList.every(item => !item.selected);

    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
      selectAllCheckbox.checked = allSelected;
      selectAllCheckbox.indeterminate = !allSelected && !noneSelected;
    }
  },

  /**
   * 更新选中项目
   */
  updateSelectedItems() {
    this.state.selectedItems = this.state.auditDataList.filter(item => item.selected);
    this.updateSelectedCount();
    this.updateBatchAuditButton();
  },

  /**
   * 更新选中数量显示
   */
  updateSelectedCount() {
    const countElement = document.getElementById('selectedCount');
    if (countElement) {
      countElement.textContent = this.state.selectedItems.length;
    }
  },

  /**
   * 更新批量审核按钮状态
   */
  updateBatchAuditButton() {
    const btn = document.getElementById('batchAuditBtn');
    if (btn) {
      btn.disabled = this.state.selectedItems.length === 0;
    }
  },

  /**
   * 批量审核
   */
  async batchAudit() {
    if (this.state.selectedItems.length === 0) {
      showWarning('请先选择要审核的项目');
      return;
    }

    try {
      await confirmDialog(`确定要批量通过 ${this.state.selectedItems.length} 个项目吗？`, '批量审核');

      this.state.batchAuditing = true;
      this.showBatchAuditLoading(true);

      for (const item of this.state.selectedItems) {
        try {
          await this.approveItemInternal(item);
        } catch (error) {
          console.error(`审核项目 ${item.id} 失败:`, error);
        }
      }

      showSuccess('批量审核完成');
      await this.loadAuditData();

    } catch (error) {
      if (error !== 'cancel') {
        console.error('批量审核失败:', error);
        showError('批量审核失败');
      }
    } finally {
      this.state.batchAuditing = false;
      this.showBatchAuditLoading(false);
    }
  },

  /**
   * 显示/隐藏批量审核加载状态
   */
  showBatchAuditLoading(show) {
    const spinner = document.getElementById('batchAuditSpinner');
    const btn = document.getElementById('batchAuditBtn');

    if (spinner && btn) {
      if (show) {
        spinner.classList.remove('d-none');
        btn.disabled = true;
      } else {
        spinner.classList.add('d-none');
        btn.disabled = this.state.selectedItems.length === 0;
      }
    }
  },

  /**
   * 通过审核
   */
  async approveItem(index) {
    const item = this.state.auditDataList[index];
    item.approving = true;
    this.showApproveLoading(index, true);

    try {
      await this.approveItemInternal(item);
      showSuccess('审核通过');
      await this.loadAuditData();
    } catch (error) {
      console.error('审核失败:', error);
      showError('审核失败');
    } finally {
      item.approving = false;
      this.showApproveLoading(index, false);
    }
  },

  /**
   * 显示/隐藏通过按钮加载状态
   */
  showApproveLoading(index, show) {
    const spinner = document.getElementById(`approveSpinner_${index}`);
    const btn = document.getElementById(`approveBtn_${index}`);

    if (spinner && btn) {
      if (show) {
        spinner.classList.remove('d-none');
        btn.disabled = true;
      } else {
        spinner.classList.add('d-none');
        btn.disabled = false;
      }
    }
  },

  /**
   * 内部审核通过方法
   */
  async approveItemInternal(item) {
    const response = await ApiClient.post('/whauditimage/', {
      collectionId: this.state.selectedCollection,
      dataId: item.id,
      action: 'approve'
    });

    if (response.code !== 200) {
      throw new Error(response.message || '审核失败');
    }
  },

  /**
   * 拒绝审核
   */
  rejectItem(index) {
    const item = this.state.auditDataList[index];
    this.state.currentRejectItem = item;
    this.state.rejectForm = {
      reason: '',
      note: ''
    };

    // 清空表单
    document.getElementById('rejectReason').value = '';
    document.getElementById('rejectNote').value = '';

    // 显示对话框
    const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
    modal.show();
  },

  /**
   * 确认拒绝
   */
  async confirmReject() {
    const reason = document.getElementById('rejectReason').value;
    const note = document.getElementById('rejectNote').value;

    if (!reason) {
      showWarning('请选择拒绝原因');
      return;
    }

    this.state.rejecting = true;
    this.showRejectLoading(true);

    try {
      const response = await ApiClient.post('/whauditimage/', {
        collectionId: this.state.selectedCollection,
        dataId: this.state.currentRejectItem.id,
        action: 'reject',
        reason: reason,
        note: note
      });

      if (response.code === 200) {
        showSuccess('已拒绝该项目');
        const modal = bootstrap.Modal.getInstance(document.getElementById('rejectModal'));
        modal.hide();
        await this.loadAuditData();
      } else {
        showError('拒绝失败');
      }
    } catch (error) {
      console.error('拒绝失败:', error);
      showError('拒绝失败');
    } finally {
      this.state.rejecting = false;
      this.showRejectLoading(false);
    }
  },

  /**
   * 显示/隐藏拒绝按钮加载状态
   */
  showRejectLoading(show) {
    const spinner = document.getElementById('rejectSpinner');
    const btn = document.getElementById('confirmRejectBtn');

    if (spinner && btn) {
      if (show) {
        spinner.classList.remove('d-none');
        btn.disabled = true;
      } else {
        spinner.classList.add('d-none');
        btn.disabled = false;
      }
    }
  },

  /**
   * 编辑项目
   */
  editItem(index) {
    const item = this.state.auditDataList[index];
    this.state.editForm = {
      id: item.id,
      q: item.q,
      a: item.a,
      auditNote: ''
    };

    // 填充表单
    document.getElementById('editQ').value = item.q;
    document.getElementById('editA').value = item.a || '';
    document.getElementById('editAuditNote').value = '';

    // 显示对话框
    const modal = new bootstrap.Modal(document.getElementById('editModal'));
    modal.show();
  },

  /**
   * 保存编辑
   */
  async saveEdit() {
    const q = document.getElementById('editQ').value;
    const a = document.getElementById('editA').value;
    const auditNote = document.getElementById('editAuditNote').value;

    this.state.saving = true;
    this.showSaveEditLoading(true);

    try {
      const response = await ApiClient.post('/updateDatasetdatas/', {
        data: [this.state.editForm.id, q, a, []]
      });

      if (response.code === 200) {
        showSuccess('保存成功');
        const modal = bootstrap.Modal.getInstance(document.getElementById('editModal'));
        modal.hide();
        await this.loadAuditData();
      } else {
        showError('保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      showError('保存失败');
    } finally {
      this.state.saving = false;
      this.showSaveEditLoading(false);
    }
  },

  /**
   * 显示/隐藏保存编辑加载状态
   */
  showSaveEditLoading(show) {
    const spinner = document.getElementById('saveEditSpinner');
    const btn = document.getElementById('saveEditBtn');

    if (spinner && btn) {
      if (show) {
        spinner.classList.remove('d-none');
        btn.disabled = true;
      } else {
        spinner.classList.add('d-none');
        btn.disabled = false;
      }
    }
  },

  /**
   * 预览图片
   */
  previewImage(src) {
    document.getElementById('previewImage').src = src;
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
  },

  /**
   * 处理菜单选择
   */
  handleMenuSelect(index) {
    if (index === '2') {
      window.router.navigate('knowledge-base');
    } else if (index === '1') {
      window.router.navigate('chat-logs');
    } else if (index === '3') {
      window.router.navigate('image-management');
    } else if (index === '4') {
      this.state.activeMenu = index;
    }
  }
};

// 审核应用组件（兼容旧的路由系统）
const AuditApp = {
  init() {
    AuditManager.init();
  }
};

// 导出到全局
window.AuditManager = AuditManager;
window.AuditApp = AuditApp;
