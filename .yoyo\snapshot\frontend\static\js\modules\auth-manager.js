/**
 * 认证管理模块
 * 处理用户登录、权限验证等操作
 */

const AuthManager = {
  /**
   * 用户登录
   */
  async login(username, password) {
    try {
      const response = await ApiClient.post('/login/', { username, password });
      return response;
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  },

  /**
   * 用户退出
   */
  logout() {
    // 清除本地存储
    StorageManager.remove('token');
    StorageManager.remove('sfilesh');
    
    // 显示成功消息
    showSuccess('退出成功');
    
    return true;
  },

  /**
   * 获取当前用户token
   */
  getToken() {
    return StorageManager.get('token', '');
  },

  /**
   * 设置用户token
   */
  setToken(token) {
    StorageManager.set('token', token);
  },

  /**
   * 检查是否已登录
   */
  isLoggedIn() {
    const token = this.getToken();
    return !!token;
  },

  /**
   * 获取用户权限状态
   */
  getPermissionStatus() {
    return StorageManager.get('sfilesh', false);
  },

  /**
   * 设置用户权限状态
   */
  setPermissionStatus(status) {
    StorageManager.set('sfilesh', status);
  },

  /**
   * 检查是否有管理员权限
   */
  hasAdminPermission() {
    return this.getPermissionStatus();
  },

  /**
   * 初始化认证状态
   */
  initAuthState() {
    const token = this.getToken();
    const hasPermission = this.getPermissionStatus();
    
    return {
      isLoggedIn: !!token,
      hasPermission: hasPermission,
      token: token
    };
  },

  /**
   * 验证登录表单
   */
  validateLoginForm(username, password) {
    if (!username || !username.trim()) {
      showError('请输入用户名');
      return false;
    }
    
    if (!password || !password.trim()) {
      showError('请输入密码');
      return false;
    }
    
    return true;
  }
};

// 导出到全局
window.AuthManager = AuthManager;
