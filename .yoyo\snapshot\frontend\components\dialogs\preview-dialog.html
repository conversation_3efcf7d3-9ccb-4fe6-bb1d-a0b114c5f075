<!-- 文件预览对话框组件 -->
<el-dialog
  :title="'预览: ' + (previewData.name || '')"
  :visible.sync="previewDialogVisible"
  width="90%"
  top="5vh"
  class="preview-modal"
  :close-on-click-modal="false">
  
  <div v-if="previewData.audit == 0" style="margin-bottom: 20px;">
    <el-alert
      title="此文档未审核"
      type="warning"
      :closable="false"
      show-icon>
    </el-alert>
  </div>
  
  <el-tabs v-model="activeTab" class="custom-tabs">
    <el-tab-pane label="数据内容" name="content">
      <div v-for="(item, index) in previewData.q" :key="index" class="preview-card-container">
        <div class="preview-card">
          <div class="IDstyle">{{ item[0] }}</div>
          
          <el-tabs v-model="item.activeSubTab" type="border-card">
            <el-tab-pane label="问题" :name="'q' + index">
              <div v-html="item[1]"></div>
            </el-tab-pane>
            
            <el-tab-pane label="答案" :name="'a' + index">
              <el-input
                type="textarea"
                v-model="item[2]"
                :rows="6"
                placeholder="请输入答案">
              </el-input>
            </el-tab-pane>
            
            <el-tab-pane label="索引" :name="'index' + index">
              <div v-for="(indexItem, indexIndex) in item[3]" :key="indexIndex" class="index-item">
                <div v-if="indexItem._id" class="default-index">
                  <div class="index-content">
                    <div><strong>默认索引:</strong> {{ indexItem.text }}</div>
                  </div>
                </div>
                
                <div v-else class="custom-index" @click="indexItem.editing = !indexItem.editing">
                  <div class="index-content" v-if="!indexItem.editing">
                    <div><strong>自定义索引:</strong> {{ indexItem.text }}</div>
                  </div>
                  <el-input
                    v-else
                    v-model="indexItem.text"
                    @blur="indexItem.editing = false"
                    @keyup.enter.native="indexItem.editing = false"
                    placeholder="请输入索引内容">
                  </el-input>
                </div>
              </div>
              
              <el-button
                type="primary"
                size="small"
                icon="el-icon-plus"
                @click="addNewIndex(item[3])"
                style="margin-top: 10px;">
                新增索引
              </el-button>
            </el-tab-pane>
          </el-tabs>
          
          <div class="delete-icon" @click="deleteQAItem(item[0], index)">
            <i class="el-icon-delete"></i>
          </div>
        </div>
      </div>
    </el-tab-pane>
  </el-tabs>
  
  <div slot="footer" class="dialog-footer">
    <el-button @click="previewDialogVisible = false">关闭</el-button>
    <el-button type="primary" @click="savePreviewData" :loading="saving">保存修改</el-button>
  </div>
</el-dialog>
