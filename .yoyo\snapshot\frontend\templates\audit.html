{% extends "base.html" %}

{% block title %}内容审核{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/frontend/static/css/audit.css">
{% endblock %}

{% block content %}
<el-container class="main-container">
  <!-- 侧边栏导航 -->
  {% include 'components/layout/sidebar.html' %}

  <!-- 右侧内容 -->
  <el-main style="padding:0">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <el-select v-model="selectedDataset" placeholder="选择数据集" style="width: 200px; margin-right: 10px;" @change="loadCollections">
        <el-option
          v-for="dataset in datasets"
          :key="dataset.id"
          :label="dataset.name"
          :value="dataset.id">
        </el-option>
      </el-select>

      <el-select v-model="selectedCollection" placeholder="选择集合" style="width: 200px; margin-right: 10px;" @change="loadAuditData">
        <el-option
          v-for="collection in collections"
          :key="collection.id"
          :label="collection.name"
          :value="collection.id">
        </el-option>
      </el-select>

      <el-button type="primary" @click="loadAuditData" :loading="loading">查询</el-button>
      <el-button type="success" @click="batchAudit" :disabled="selectedItems.length === 0" :loading="batchAuditing">批量审核</el-button>
    </div>
    
    <!-- 审核数据展示 -->
    <div class="content-area">
      <div v-if="auditDataList.length === 0 && !loading" class="empty-state">
        <i class="el-icon-document-checked"></i>
        <p>暂无待审核数据</p>
      </div>
      
      <div v-else class="audit-data-list">
        <div class="batch-actions">
          <el-checkbox v-model="selectAll" @change="handleSelectAll">全选</el-checkbox>
          <span class="selected-count">已选择 {{ selectedItems.length }} 项</span>
        </div>
        
        <div v-for="(item, index) in auditDataList" :key="index" class="audit-data-item">
          <el-card class="audit-card">
            <div class="audit-header">
              <el-checkbox v-model="item.selected" @change="handleItemSelect"></el-checkbox>
              <span class="data-id">ID: {{ item.id }}</span>
              <div class="audit-actions">
                <el-button type="success" size="small" @click="approveItem(item)" :loading="item.approving">通过</el-button>
                <el-button type="danger" size="small" @click="rejectItem(item)" :loading="item.rejecting">拒绝</el-button>
                <el-button type="text" @click="editItem(item)" icon="el-icon-edit">编辑</el-button>
              </div>
            </div>
            
            <div class="audit-content">
              <div class="question-section">
                <h4>问题内容：</h4>
                <div class="content-display" v-html="item.processedQ"></div>
              </div>
              
              <div class="answer-section" v-if="item.a">
                <h4>答案内容：</h4>
                <div class="content-display">{{ item.a }}</div>
              </div>
              
              <div class="images-section" v-if="item.images && item.images.length > 0">
                <h4>相关图片：</h4>
                <div class="images-grid">
                  <div v-for="(image, imgIndex) in item.images" :key="imgIndex" class="image-item">
                    <img :src="image.src" @click="previewImage(image)" class="thumbnail">
                  </div>
                </div>
              </div>
              
              <div class="metadata-section" v-if="item.metadata">
                <h4>元数据信息：</h4>
                <div class="metadata-grid">
                  <div class="metadata-item" v-for="(value, key) in item.metadata" :key="key">
                    <span class="metadata-key">{{ key }}:</span>
                    <span class="metadata-value">{{ value }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </el-main>
</el-container>

<!-- 编辑数据对话框 -->
<el-dialog
  title="编辑审核数据"
  :visible.sync="editDialogVisible"
  width="80%"
  top="5vh">
  
  <el-form :model="editForm" label-width="100px">
    <el-form-item label="问题内容">
      <el-input
        type="textarea"
        v-model="editForm.q"
        :rows="6"
        placeholder="请输入问题内容">
      </el-input>
    </el-form-item>
    
    <el-form-item label="答案内容">
      <el-input
        type="textarea"
        v-model="editForm.a"
        :rows="4"
        placeholder="请输入答案内容">
      </el-input>
    </el-form-item>
    
    <el-form-item label="审核备注">
      <el-input
        type="textarea"
        v-model="editForm.auditNote"
        :rows="3"
        placeholder="请输入审核备注">
      </el-input>
    </el-form-item>
  </el-form>
  
  <div slot="footer" class="dialog-footer">
    <el-button @click="editDialogVisible = false">取消</el-button>
    <el-button type="primary" @click="saveEdit" :loading="saving">保存</el-button>
  </div>
</el-dialog>

<!-- 图片预览对话框 -->
<el-dialog
  title="图片预览"
  :visible.sync="previewDialogVisible"
  width="60%">
  
  <div class="image-preview">
    <img :src="previewImageSrc" style="width: 100%; height: auto;">
  </div>
  
  <div slot="footer" class="dialog-footer">
    <el-button @click="previewDialogVisible = false">关闭</el-button>
  </div>
</el-dialog>

<!-- 拒绝原因对话框 -->
<el-dialog
  title="拒绝原因"
  :visible.sync="rejectDialogVisible"
  width="500px">
  
  <el-form :model="rejectForm" label-width="100px">
    <el-form-item label="拒绝原因">
      <el-select v-model="rejectForm.reason" placeholder="请选择拒绝原因" style="width: 100%;">
        <el-option label="内容不当" value="inappropriate"></el-option>
        <el-option label="信息错误" value="incorrect"></el-option>
        <el-option label="格式问题" value="format"></el-option>
        <el-option label="重复内容" value="duplicate"></el-option>
        <el-option label="其他" value="other"></el-option>
      </el-select>
    </el-form-item>
    
    <el-form-item label="详细说明">
      <el-input
        type="textarea"
        v-model="rejectForm.note"
        :rows="4"
        placeholder="请输入详细说明">
      </el-input>
    </el-form-item>
  </el-form>
  
  <div slot="footer" class="dialog-footer">
    <el-button @click="rejectDialogVisible = false">取消</el-button>
    <el-button type="danger" @click="confirmReject" :loading="rejecting">确认拒绝</el-button>
  </div>
</el-dialog>
{% endblock %}

{% block extra_js %}
<script src="/frontend/static/js/audit.js"></script>
{% endblock %}
