"""
认证业务服务
处理用户认证和权限管理
"""

from typing import Dict, Any
from config.settings import settings
from utils.file_utils import get_token


class AuthService:
    """认证业务服务类"""
    
    def login(self, username: str, password: str) -> Dict[str, Any]:
        """用户登录"""
        try:
            if username == settings.BASE_USERNAME and password == settings.BASE_PASSWORD:
                token = get_token(settings.BASE_USERNAME, settings.BASE_PASSWORD)
                return {
                    "code": 200,
                    "message": "登录成功",
                    "token": token
                }
            else:
                return {
                    "code": 401,
                    "message": "账号或密码错误"
                }
        except Exception as e:
            return {
                "code": 500,
                "message": str(e)
            }
    
    def verify_token(self, token: str) -> bool:
        """验证token"""
        expected_token = get_token(settings.BASE_USERNAME, settings.BASE_PASSWORD)
        return token == expected_token
    
    def has_admin_permission(self, token: str) -> bool:
        """检查是否有管理员权限"""
        return self.verify_token(token)


# 创建全局服务实例
auth_service = AuthService()
