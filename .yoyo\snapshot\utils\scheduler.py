"""
定时任务调度器
处理系统的定时任务
"""

import re
from typing import List, Dict, Any
from bson.objectid import ObjectId
from zhipuai import ZhipuAI

from ..database.mongodb import get_database
from ..config.settings import settings


class SchedulerService:
    """定时任务服务类"""
    
    def __init__(self):
        self.client = ZhipuAI(api_key=settings.zhipu_key) if hasattr(settings, 'zhipu_key') else None
    
    def sync_indexs(self):
        """同步索引 - 从zhishiku_h5.py迁移"""
        try:
            db = get_database()
            collections = list(db["dataset_collections"].find({"metadata.isindex": "0"}))
            
            for coll in collections:
                metadata = coll.get("metadata", {})
                if metadata.get("source") == "bs":
                    prompt = getattr(settings, 'bs_prompt', '')
                else:
                    prompt = getattr(settings, 'system_prompt', '')
                
                dataids_list = list(db['dataset_datas'].find({"collectionId": coll["_id"]}).sort({"chunkIndex": 1}))
                
                # 如果已经有数据正在处理，则跳过
                if dataids_list and db["dataset_trainings"].count_documents({"collectionId": coll["_id"]}) > 0:
                    continue
                
                for data in dataids_list:
                    try:
                        newindexs = self.get_indexs(data["indexes"], 1)
                        data["q"] = self.get_image(data["q"])
                        
                        name = "文件名为：" + coll["name"]
                        if coll.get("cpxh"):
                            name += "，产品型号为：" + coll["cpxh"]
                        if coll.get("cpmc"):
                            name += "，产品名称为：" + coll["cpmc"]
                        
                        # 这里可以添加更多的索引同步逻辑
                        print(f"处理数据: {data['_id']}")
                        
                    except Exception as e:
                        print(f"处理数据失败: {e}")
                        continue
            
            print("索引同步完成")
        except Exception as e:
            print(f"索引同步失败: {e}")
    
    def get_indexs(self, indexes: List[Dict], tag: int = 0) -> List[Dict]:
        """获取索引 - 从zhishiku_h5.py迁移"""
        result = []
        for index in indexes:
            if index.get("defaultIndex") == True or index.get("type") == "default":
                index["_id"] = str(index["_id"])
                result.append(index)
            else:
                if tag == 1:
                    text = {"text": index["text"]}
                else:
                    text = {"text": index["text"], "editing": False}
                result.append(text)
        return result
    
    def get_image(self, q: str) -> str:
        """处理图片 - 从zhishiku_h5.py迁移"""
        pattern = r'!\[([^\]]*)\]\((/api/system/img/[^)]+\.(?:png|jpg|jpeg))\)'
        replacement = f'<image src="http://{settings.api_ip}:3000' + r'\2" alt="\1" loading="lazy" referrerpolicy="no-referrer"/>'
        new_string = re.sub(pattern, replacement, q)
        return new_string
    
    def del_image(self, q: str) -> str:
        """删除图片 - 从zhishiku_h5.py迁移"""
        # 定义正则表达式模式
        pattern = f'<image src="http://{settings.api_ip}:3000' + r'(/api/system/img/[^"]+\.(?:png|jpg|jpeg))" alt="([^"]*)" loading="lazy" referrerpolicy="no-referrer"/>'
        # 还原为 ![](...)
        replacement = r'![\2](\1)'
        # 使用 re.sub 进行还原
        original_string = re.sub(pattern, replacement, q)
        return original_string


# 创建服务实例
scheduler_service = SchedulerService()

# 导出函数以保持兼容性
def sync_indexs():
    """定时任务函数 - 保持与原有代码的兼容性"""
    scheduler_service.sync_indexs()
