<!-- 文件预览对话框组件 -->
<div class="modal fade" id="previewModal" tabindex="-1">
  <div class="modal-dialog modal-fullscreen">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">预览: <span id="previewFileName"></span></h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div id="previewWarning" class="alert alert-warning d-none" role="alert">
          <i class="bi bi-exclamation-triangle me-2"></i>
          此文档未审核
        </div>

        <!-- 标签页导航 -->
        <ul class="nav nav-tabs" id="previewTabs" role="tablist">
          <li class="nav-item" role="presentation">
            <button class="nav-link active" id="content-tab" data-bs-toggle="tab" data-bs-target="#content" type="button" role="tab">
              数据内容
            </button>
          </li>
        </ul>

        <!-- 标签页内容 -->
        <div class="tab-content" id="previewTabContent">
          <div class="tab-pane fade show active" id="content" role="tabpanel">
            <div id="previewDataContainer" class="mt-3">
              <!-- 数据内容将通过JavaScript动态生成 -->
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        <button type="button" class="btn btn-primary" id="savePreviewBtn" onclick="savePreviewData()">
          <span class="spinner-border spinner-border-sm d-none me-1" id="saveSpinner"></span>
          保存修改
        </button>
      </div>
    </div>
  </div>
</div>
