/**
 * 文件管理模块
 * 处理文件上传、预览、删除等操作
 */

const FileManager = {
  /**
   * 获取文件信息
   */
  async getFileInfo(filename) {
    try {
      const response = await ApiClient.get('/getFileInfo/', { filename });
      return response;
    } catch (error) {
      console.error('获取文件信息失败:', error);
      throw error;
    }
  },

  /**
   * 上传文件
   */
  async uploadFile(file, type, processData, onProgress = null) {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);
      formData.append('data', JSON.stringify(processData));

      const response = await ApiClient.upload('/uploadfiles/', formData, onProgress);
      return response;
    } catch (error) {
      console.error('文件上传失败:', error);
      throw error;
    }
  },

  /**
   * 验证文件上传前的条件
   */
  validateBeforeUpload(file, selectedLibrary, processingParam, processingMethod, chunkLength) {
    if (!selectedLibrary) {
      showError('请先选择知识库');
      return false;
    }

    // 自定义参数时的验证
    if (processingParam === 'custom') {
      const min = processingMethod === 'chunk' ? 100 : 4000;
      const max = processingMethod === 'chunk' ? 1800 : 16000;

      if (!chunkLength || chunkLength < min || chunkLength > max) {
        showError(`分块长度需在${min}-${max}之间`);
        return false;
      }
    }

    return true;
  },

  /**
   * 构建处理参数
   */
  buildProcessParams(processingMethod, processingParam, chunkLength, customSeparator, fileInfo = null) {
    const processData = {
      trainingType: processingMethod,
      chunkSize: processingParam === 'auto' 
        ? (processingMethod === 'chunk' ? 512 : 4096) 
        : chunkLength,
      chunkSplitter: processingParam === 'custom' ? customSeparator : '',
      processingParam: processingParam
    };

    // 如果有文件信息，添加到处理参数中
    if (fileInfo) {
      processData.fileInfo = fileInfo;
    }

    return processData;
  },

  /**
   * 批量上传文件
   */
  async uploadMultipleFiles(files, selectedLibrary, processParams, onProgress = null, onFileComplete = null) {
    const results = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      try {
        const result = await this.uploadFile(
          file.raw || file, 
          selectedLibrary, 
          processParams, 
          onProgress
        );
        
        results.push({ file: file.name, success: true, result });
        
        if (onFileComplete) {
          onFileComplete(file, true, result);
        }
        
      } catch (error) {
        results.push({ file: file.name, success: false, error });
        
        if (onFileComplete) {
          onFileComplete(file, false, error);
        }
      }
    }
    
    return results;
  },

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  /**
   * 验证文件类型
   */
  validateFileType(file, allowedTypes = []) {
    if (allowedTypes.length === 0) return true;
    
    const fileExtension = file.name.split('.').pop().toLowerCase();
    return allowedTypes.includes(fileExtension);
  },

  /**
   * 验证文件大小
   */
  validateFileSize(file, maxSizeMB = 100) {
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    return file.size <= maxSizeBytes;
  }
};

// 导出到全局
window.FileManager = FileManager;
