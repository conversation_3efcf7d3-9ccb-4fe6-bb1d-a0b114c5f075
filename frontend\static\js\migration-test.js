/**
 * Vue到Bootstrap迁移测试脚本
 */

// 测试结果收集器
const TestResults = {
  results: [],
  
  log(testName, passed, message = '') {
    const result = {
      test: testName,
      passed: passed,
      message: message,
      timestamp: new Date().toISOString()
    };
    this.results.push(result);
    
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status}: ${testName}${message ? ' - ' + message : ''}`);
  },
  
  summary() {
    const total = this.results.length;
    const passed = this.results.filter(r => r.passed).length;
    const failed = total - passed;
    
    console.log('\n=== 测试总结 ===');
    console.log(`总测试数: ${total}`);
    console.log(`通过: ${passed}`);
    console.log(`失败: ${failed}`);
    console.log(`成功率: ${((passed / total) * 100).toFixed(1)}%`);
    
    if (failed > 0) {
      console.log('\n失败的测试:');
      this.results.filter(r => !r.passed).forEach(r => {
        console.log(`- ${r.test}: ${r.message}`);
      });
    }
    
    return { total, passed, failed };
  }
};

// 基础功能测试
const BasicTests = {
  // 测试Bootstrap是否正确加载
  testBootstrapLoaded() {
    const hasBootstrap = typeof bootstrap !== 'undefined';
    TestResults.log('Bootstrap加载', hasBootstrap, hasBootstrap ? '' : 'Bootstrap未正确加载');
  },
  
  // 测试jQuery是否正确加载
  testJQueryLoaded() {
    const hasJQuery = typeof $ !== 'undefined';
    TestResults.log('jQuery加载', hasJQuery, hasJQuery ? '' : 'jQuery未正确加载');
  },
  
  // 测试公共工具函数是否可用
  testCommonUtils() {
    const hasCommonUtils = typeof CommonUtils !== 'undefined';
    TestResults.log('公共工具函数', hasCommonUtils, hasCommonUtils ? '' : 'CommonUtils未定义');
    
    if (hasCommonUtils) {
      const hasApiClient = typeof ApiClient !== 'undefined';
      TestResults.log('API客户端', hasApiClient, hasApiClient ? '' : 'ApiClient未定义');
      
      const hasStateManager = typeof StateManager !== 'undefined';
      TestResults.log('状态管理器', hasStateManager, hasStateManager ? '' : 'StateManager未定义');
      
      const hasDOMUtils = typeof DOMUtils !== 'undefined';
      TestResults.log('DOM工具', hasDOMUtils, hasDOMUtils ? '' : 'DOMUtils未定义');
    }
  },
  
  // 测试Vue是否已移除
  testVueRemoved() {
    const hasVue = typeof Vue !== 'undefined';
    TestResults.log('Vue已移除', !hasVue, hasVue ? 'Vue仍然存在' : '');
  },
  
  // 测试Element UI是否已移除
  testElementUIRemoved() {
    const hasElementUI = typeof ELEMENT !== 'undefined';
    TestResults.log('Element UI已移除', !hasElementUI, hasElementUI ? 'Element UI仍然存在' : '');
  },
  
  // 测试审核管理器是否可用
  testAuditManager() {
    const hasAuditManager = typeof AuditManager !== 'undefined';
    TestResults.log('审核管理器', hasAuditManager, hasAuditManager ? '' : 'AuditManager未定义');
  },
  
  // 测试图片管理器是否可用
  testImageManager() {
    const hasImageManager = typeof ImageManager !== 'undefined';
    TestResults.log('图片管理器', hasImageManager, hasImageManager ? '' : 'ImageManager未定义');
  },
  
  // 运行所有基础测试
  runAll() {
    console.log('开始运行基础功能测试...\n');
    
    this.testBootstrapLoaded();
    this.testJQueryLoaded();
    this.testCommonUtils();
    this.testVueRemoved();
    this.testElementUIRemoved();
    this.testAuditManager();
    this.testImageManager();
    
    return TestResults.summary();
  }
};

// DOM测试
const DOMTests = {
  // 测试页面基本结构
  testPageStructure() {
    const hasApp = document.getElementById('app') !== null;
    TestResults.log('应用容器存在', hasApp, hasApp ? '' : '#app元素不存在');
    
    const hasBootstrapCSS = document.querySelector('link[href*="bootstrap"]') !== null;
    TestResults.log('Bootstrap CSS加载', hasBootstrapCSS, hasBootstrapCSS ? '' : 'Bootstrap CSS未加载');
  },
  
  // 测试模态框功能
  testModalFunctionality() {
    try {
      // 创建测试模态框
      const modalHtml = `
        <div class="modal fade" id="testModal" tabindex="-1">
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-body">测试模态框</div>
            </div>
          </div>
        </div>
      `;
      document.body.insertAdjacentHTML('beforeend', modalHtml);
      
      const modalElement = document.getElementById('testModal');
      const modal = new bootstrap.Modal(modalElement);
      
      TestResults.log('模态框创建', true, '');
      
      // 清理测试模态框
      modalElement.remove();
    } catch (error) {
      TestResults.log('模态框创建', false, error.message);
    }
  },
  
  // 运行所有DOM测试
  runAll() {
    console.log('开始运行DOM测试...\n');
    
    this.testPageStructure();
    this.testModalFunctionality();
    
    return TestResults.summary();
  }
};

// 主测试函数
function runMigrationTests() {
  console.log('=== Vue到Bootstrap迁移测试 ===\n');
  
  // 等待DOM加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      BasicTests.runAll();
      DOMTests.runAll();
    });
  } else {
    BasicTests.runAll();
    DOMTests.runAll();
  }
}

// 导出测试函数
window.runMigrationTests = runMigrationTests;
window.TestResults = TestResults;
window.BasicTests = BasicTests;
window.DOMTests = DOMTests;

// 自动运行测试（如果在开发环境）
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
  // 延迟运行，确保所有脚本都已加载
  setTimeout(runMigrationTests, 1000);
}
