"""
数据仓库
处理数据相关的数据访问
"""

from typing import List, Dict, Any, Optional
from bson.objectid import ObjectId
from pymongo.database import Database
from ..connection import get_database


class DataRepository:
    """数据数据访问仓库"""
    
    def __init__(self, db: Database = None):
        self.db = db or get_database()
        self.collection = self.db['dataset_datas']
    
    def get_data_by_collection_id(self, collection_id: str) -> List[Dict[str, Any]]:
        """根据集合ID获取数据列表"""
        return list(self.collection.find({"collectionId": ObjectId(collection_id)}).sort({"chunkIndex": 1}))
    
    def get_data_by_id(self, data_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取单个数据"""
        return self.collection.find_one({"_id": ObjectId(data_id)})
    
    def update_data(self, data_id: str, q: str, a: str, indexes: List[Dict[str, Any]]):
        """更新数据内容"""
        self.collection.update_one(
            {"_id": ObjectId(data_id)},
            {"$set": {"q": q, "a": a, "indexes": indexes}}
        )
    
    def delete_data(self, data_id: str):
        """删除数据"""
        self.collection.delete_one({"_id": ObjectId(data_id)})
    
    def count_data_by_collection_id(self, collection_id: str) -> int:
        """统计集合中的数据数量"""
        return self.collection.count_documents({"collectionId": ObjectId(collection_id)})


# 创建全局仓库实例
data_repository = DataRepository()
