"""
文件管理业务服务
处理文件上传、处理、删除等业务逻辑
"""

import json
import requests
from typing import Dict, Any, Optional
from urllib.parse import quote
from bson.objectid import ObjectId

from database.connection import get_database
from config.settings import settings
from config.constants import KNOWLEDGE_BASE_PROMPTS
from utils.ai_client import ai_client
from utils.file_utils import get_filename_from_url, get_filename_from_headers
from core.knowledge_base.services import knowledge_base_service


class FileManagementService:
    """文件管理业务服务类"""
    
    def __init__(self):
        self.db = get_database()
    
    def get_file_info(self, filename: str) -> Dict[str, str]:
        """获取文件信息"""
        return ai_client.extract_file_info(filename)
    
    def extract_product_model(self, collection_data: Dict[str, Any], metadata: Dict[str, Any]) -> Dict[str, Any]:
        """提取产品型号"""
        try:
            cpxh = ai_client.extract_product_model(collection_data["name"])
            if cpxh:
                metadata["cpxh"] = cpxh
        except Exception as e:
            print(f"提取产品型号发生错误: {e}")
        return metadata
    
    def upload_file(self, file_content: bytes, content_type: str, filename: str, 
                   dataset_id: str, process_params: Dict[str, Any]) -> Dict[str, Any]:
        """上传文件到知识库"""
        try:
            # 获取数据集信息
            dataset_name = knowledge_base_service.global_datasets_dic.get(dataset_id, "")
            dataset_intro = self._get_dataset_intro(dataset_name)
            
            # 构建QA提示词
            qa_prompt = self._build_qa_prompt(dataset_name, dataset_intro, filename, process_params)
            
            # 准备上传数据
            upload_data = {
                "datasetId": dataset_id,
                "parentId": None,
                "trainingType": process_params["trainingType"],
                "chunkSize": process_params["chunkSize"],
                "chunkSplitter": process_params["chunkSplitter"],
                "qaPrompt": qa_prompt if process_params.get("processingParam") == "custom" and process_params["trainingType"] == "qa" else "",
                "metadata": {
                    "source": "api",
                    "audit": "0",
                    "size": len(file_content)
                }
            }
            
            # 添加文件信息到metadata
            if process_params.get("fileInfo"):
                file_info = process_params["fileInfo"]
                upload_data["metadata"].update({
                    "型号": file_info.get("型号", "未知"),
                    "名称": file_info.get("名称", "未知"),
                    "售前售后": file_info.get("售前售后", "未知"),
                    "可接入软件": file_info.get("可接入软件", "未知")
                })
            
            # 调用FastGPT API上传文件
            response = self._upload_to_fastgpt(file_content, content_type, filename, upload_data)
            
            if response["code"] == 200:
                # 更新集合信息
                collection_id = ObjectId(response["data"]["collectionId"])
                self._update_collection_metadata(collection_id, process_params, len(file_content))
                
                # 同步到北京API
                self._sync_to_beijing_api(file_content, content_type, qa_prompt, filename, dataset_name)
                
                return {"code": 200}
            else:
                return response
                
        except Exception as e:
            print(f"文件上传发生错误: {e}")
            return {"code": 500, "message": str(e)}
    
    def delete_collection(self, collection_id: str) -> Dict[str, Any]:
        """删除集合"""
        try:
            url = f"http://{settings.API_IP}:3000/api/core/dataset/collection/delete?id={collection_id}"
            response = requests.delete(url, headers=settings.fastgpt_headers).json()
            return response
        except Exception as e:
            return {"code": 500, "message": str(e)}
    
    def save_biaoshu_files(self, url_list: list) -> Dict[str, Any]:
        """保存标书文件"""
        try:
            collection_list = []
            file_content = b""
            filename = ""
            content_type = ""
            doc_tag = False
            
            for url in url_list:
                file_url = "https://zktecoaihub.com" + url
                response = requests.get(file_url)
                
                filename_from_url = get_filename_from_url(file_url)
                file_content += response.content
                
                if not doc_tag and "doc" in filename_from_url:
                    doc_tag = True
                    
                filename = filename_from_url
                content_type = response.headers.get("Content-Type", "application/octet-stream")
            
            # 上传到标书数据集
            upload_data = {
                "datasetId": settings.BIAOSHU_DATASET_ID,
                "parentId": "",
                "trainingType": "chunk",
                "chunkSize": 1800,
                "chunkSplitter": "",
                "qaPrompt": ""
            }
            
            response = self._upload_to_fastgpt(file_content, content_type, filename, upload_data)
            
            if response["code"] == 200:
                collection_id = ObjectId(response["data"]["collectionId"])
                collection_list.append(str(collection_id))
                
                # 更新metadata
                metadata = {"source": "bs", "isindex": "0"}
                self.db['dataset_collections'].update_one(
                    {"_id": collection_id},
                    {"$set": {"metadata": metadata}}
                )
                
            return {"code": 200, "data": collection_list}
            
        except Exception as e:
            return {"code": 500, "message": str(e)}
    
    def _get_dataset_intro(self, dataset_name: str) -> str:
        """获取数据集介绍"""
        zhishiku_sm_dict = {
            a["name"]: a["intro"] 
            for a in self.db['datasets'].find({"parentId": ObjectId(settings.PARENT_DATASET_ID)})
        }
        return zhishiku_sm_dict.get(dataset_name, "")
    
    def _build_qa_prompt(self, dataset_name: str, dataset_intro: str, filename: str, process_params: Dict[str, Any]) -> str:
        """构建QA提示词"""
        qa_prompt_base = KNOWLEDGE_BASE_PROMPTS.get(dataset_name, "")
        
        qa_prompt = f'''知识库名称是{dataset_name}，知识库说明是{dataset_intro},文件名称是{filename}。
                Role: 知识库内容优化专家和问答生成工程师
                - Background: 用户需要对知识库内容进行优化，以便生成与文件内容强相关且准确的问答对。知识库包含产品介绍、功能、使用说明、接线图、部署和问题处理等多方面信息，用户期望从多维度生成最多50个问题，且问题和答案需避免重复和类似，确保内容的准确性和多样性。
                - Profile: 你是一位精通知识库管理和问答系统构建的专家，对文本内容的拆分、理解和重组有着丰富的经验。你擅长从复杂的产品文档中提取关键信息，并以清晰、准确的方式生成问答对，确保每个问题都能精准地对应文件内容的某个方面。
                - Skills: 你具备文本分析、信息提取、自然语言处理和问答系统设计的能力，能够高效地从产品文档中识别出不同维度的内容，并生成多样化的问答对，同时确保答案的完整性和准确性。
                - Goals: 根据知识库说明、文件名称和文件内容，生成最多50个与文件内容强相关的问答对，确保问题和答案的准确性和多样性，避免重复和类似内容。
                - Constrains: 生成的问答对需紧密围绕文件内容，确保答案详细完整且尽可能保留原文描述，问题需明确且具有针对性，避免模糊和重复。
                - OutputFormat: 以问答对的形式输出，每个问题前需带上产品型号或产品名称，问题和答案需紧密相关且准确。
                - Workflow:
                1. 详细阅读和理解知识库说明、文件名称及文件内容，识别其中的关键信息和不同维度的内容。
                2. 根据文件内容的不同部分（如产品介绍、功能、使用说明等），生成针对性的问题，并确保问题的多样性和准确性。
                3. 对应每个问题，从文件内容中提取详细且准确的答案，尽可能保留原文描述，确保答案的完整性和准确性。
                以下是知识库背景和示例： {qa_prompt_base}
                '''
        return qa_prompt
    
    def _upload_to_fastgpt(self, file_content: bytes, content_type: str, filename: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """上传文件到FastGPT"""
        data_json = json.dumps(data)
        files = {
            "file": (quote(filename), file_content, content_type),
            "data": (None, data_json, "application/json")
        }
        
        url = f"http://{settings.API_IP}:3000/api/core/dataset/collection/create/localFile"
        response = requests.post(url, headers=settings.fastgpt_headers, files=files)
        return response.json()
    
    def _update_collection_metadata(self, collection_id: ObjectId, process_params: Dict[str, Any], file_size: int):
        """更新集合元数据"""
        collection = self.db['dataset_collections'].find_one({"_id": collection_id})
        metadata = collection["metadata"]
        
        # 提取产品型号
        metadata = self.extract_product_model(collection, metadata)
        
        # 更新基础信息
        metadata.update({
            "source": "api",
            "audit": "0",
            "isindex": "0",
            "size": file_size
        })
        
        # 添加文件信息
        if process_params.get("fileInfo"):
            file_info = process_params["fileInfo"]
            metadata.update({
                "型号": file_info.get("型号", "未知"),
                "名称": file_info.get("名称", "未知"),
                "售前售后": file_info.get("售前售后", "未知"),
                "可接入软件": file_info.get("可接入软件", "未知")
            })
        
        # 更新数据库
        self.db['dataset_collections'].update_one(
            {"_id": collection_id},
            {"$set": {"metadata": metadata, "forbid": True}}
        )
    
    def _sync_to_beijing_api(self, file_content: bytes, content_type: str, qa_prompt: str, filename: str, dataset_name: str):
        """同步到北京API"""
        try:
            zhishiku_ss_dict = {
                a["name"]: str(a["_id"]) 
                for a in self.db['dataset_collections'].find({
                    "datasetId": ObjectId(settings.BEIJING_DATASET_ID),
                    "type": "folder"
                })
            }
            parent_id = zhishiku_ss_dict.get(dataset_name, '')
            
            data = {
                "datasetId": settings.BEIJING_DATASET_ID,
                "parentId": parent_id,
                "trainingType": "qa",
                "chunkSize": 4000,
                "chunkSplitter": "",
                "qaPrompt": qa_prompt
            }
            
            data_json = json.dumps(data)
            files = {
                "file": (quote(filename), file_content, content_type),
                "data": (None, data_json, "application/json")
            }
            
            url = f"http://{settings.API_IP}:3000/api/core/dataset/collection/create/localFile"
            requests.post(url, headers=settings.fastgpt_headers, files=files)
            
        except Exception as e:
            print(f"同步到北京API发生错误: {e}")


# 创建全局服务实例
file_management_service = FileManagementService()
