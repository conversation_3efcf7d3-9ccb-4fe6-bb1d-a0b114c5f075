<!-- 文件上传对话框组件 -->
<div class="modal fade" id="uploadModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">导入数据</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" onclick="resetUploadForm()"></button>
      </div>
      <div class="modal-body">
        <form id="uploadForm">
          <div class="mb-3">
            <label for="uploadLibrary" class="form-label">选择知识库 <span class="text-danger">*</span></label>
            <select class="form-select" id="uploadLibrary" name="selectedLibrary" required>
              <option value="">请选择知识库</option>
              <!-- 选项将通过JavaScript动态添加 -->
            </select>
            <div class="invalid-feedback">
              请选择知识库
            </div>
          </div>

          <div class="mb-3">
            <label class="form-label">处理方式</label>
            <div class="form-check">
              <input class="form-check-input" type="radio" name="processingMethod" id="chunkMode" value="chunk" checked>
              <label class="form-check-label" for="chunkMode">
                分块模式
              </label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="radio" name="processingMethod" id="qaMode" value="qa">
              <label class="form-check-label" for="qaMode">
                问答模式
              </label>
            </div>
          </div>

          <div class="mb-3">
            <label class="form-label">参数设置</label>
            <div class="form-check">
              <input class="form-check-input" type="radio" name="processingParam" id="autoParam" value="auto" checked onchange="toggleCustomParams()">
              <label class="form-check-label" for="autoParam">
                自动
              </label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="radio" name="processingParam" id="customParam" value="custom" onchange="toggleCustomParams()">
              <label class="form-check-label" for="customParam">
                自定义
              </label>
            </div>
          </div>

          <div id="customParamsSection" class="d-none">
            <div class="mb-3">
              <label for="chunkLength" class="form-label">分块长度</label>
              <input type="number" class="form-control" id="chunkLength" name="chunkLength" value="512" min="100" max="1800">
            </div>

            <div class="mb-3">
              <label for="customSeparator" class="form-label">自定义分隔符</label>
              <input type="text" class="form-control" id="customSeparator" name="customSeparator" placeholder="可选，留空使用默认分隔符">
            </div>
          </div>

          <div class="mb-3">
            <label for="fileInput" class="form-label">选择文件</label>
            <div class="upload-area border border-2 border-dashed rounded p-4 text-center"
                 ondrop="handleFileDrop(event)"
                 ondragover="handleDragOver(event)"
                 ondragleave="handleDragLeave(event)">
              <i class="bi bi-cloud-upload fs-1 text-muted"></i>
              <div class="mt-2">
                <p class="mb-1">将文件拖到此处，或<button type="button" class="btn btn-link p-0" onclick="document.getElementById('fileInput').click()">点击上传</button></p>
                <small class="text-muted">支持多文件上传</small>
              </div>
              <input type="file" class="d-none" id="fileInput" multiple onchange="handleFileSelect(event)">
            </div>

            <!-- 文件列表 -->
            <div id="fileList" class="mt-3"></div>
          </div>
        </form>

        <!-- 上传进度 -->
        <div id="uploadProgressSection" class="d-none">
          <div class="progress">
            <div class="progress-bar" id="uploadProgressBar" role="progressbar" style="width: 0%"></div>
          </div>
          <small class="text-muted mt-1" id="uploadProgressText">准备上传...</small>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" id="uploadSubmitBtn" onclick="uploadAllFiles()" disabled>
          <span class="spinner-border spinner-border-sm d-none me-1" id="uploadSpinner"></span>
          开始上传
        </button>
      </div>
    </div>
  </div>
</div>
