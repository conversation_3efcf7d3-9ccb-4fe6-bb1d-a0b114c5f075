/**
 * 知识库管理系统 - 主要JavaScript文件
 * 使用原生JavaScript替代Vue.js
 */

// 全局应用状态
const AppState = {
  // 基础状态
  activeMenu: 'knowledge-base',
  loading: false,

  // 搜索和过滤
  searchKey: '',
  dateRange: [],
  selectedLibrary: '',
  filterStatus: '',

  // 数据
  files: [],
  filteredFiles: [],
  libraries: [],
  serverIP: '',

  // 认证相关
  sfilesh: false,
  token: '',

  // 文件上传
  selectedFiles: [],
  uploadProgress: 0,
  uploading: false,

  // 文件预览
  previewData: { name: '', q: [], audit: '1' },
  activeTab: 'content',
  saving: false,

  // 当前操作的文件
  currentFile: null,

  // 审核相关
  sameFiles: [],
  auditing: false,

  // 文件信息
  fileInfoForm: {
    型号: '',
    名称: '',
    售前售后: '',
    可接入软件: ''
  },
  currentUploadingFile: null
};

// 确保所有模块都已加载
document.addEventListener('DOMContentLoaded', function() {
  // 加载所有模块
  const modules = [
    '/frontend/static/js/modules/data-manager.js',
    '/frontend/static/js/modules/file-manager.js',
    '/frontend/static/js/modules/auth-manager.js'
  ];

  // 动态加载模块
  Promise.all(modules.map(src => {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  })).then(() => {
    // 所有模块加载完成后初始化应用
    initApp();
  }).catch(error => {
    console.error('模块加载失败:', error);
    // 即使模块加载失败也尝试初始化应用
    initApp();
  });
});

// 初始化应用
function initApp() {
  console.log('知识库管理系统初始化...');

  // 初始化事件监听器
  initEventListeners();

  // 初始化数据
  initData();

  // 更新时间显示
  updateCurrentTime();
  setInterval(updateCurrentTime, 1000);

  console.log('知识库管理系统初始化完成');
}

// 初始化事件监听器
function initEventListeners() {
  // 搜索框事件
  const searchInput = document.getElementById('searchKey');
  if (searchInput) {
    searchInput.addEventListener('input', (e) => handleSearch(e.target.value));
  }

  // 日期范围事件
  const startDate = document.getElementById('startDate');
  const endDate = document.getElementById('endDate');
  if (startDate) startDate.addEventListener('change', handleDateChange);
  if (endDate) endDate.addEventListener('change', handleDateChange);

  // 知识库选择事件
  const librarySelect = document.getElementById('selectedLibrary');
  if (librarySelect) {
    librarySelect.addEventListener('change', (e) => handleLibraryChange(e.target.value));
  }

  // 状态过滤事件
  const statusSelect = document.getElementById('filterStatus');
  if (statusSelect) {
    statusSelect.addEventListener('change', (e) => handleStatusChange(e.target.value));
  }
}

// 初始化数据
async function initData() {
  try {
    AppState.loading = true;
    showLoadingState();

    // 加载知识库列表
    await loadLibraries();

    // 加载文件列表
    await loadFiles();

    // 检查登录状态
    checkLoginStatus();

  } catch (error) {
    console.error('初始化数据失败:', error);
    showError('初始化数据失败');
  } finally {
    AppState.loading = false;
    hideLoadingState();
  }
}

// 加载知识库列表
async function loadLibraries() {
  try {
    const response = await ApiClient.get('/getDatasList/');
    if (response.code === 200) {
      AppState.libraries = response.data;
      updateLibrarySelect();
    }
  } catch (error) {
    console.error('加载知识库列表失败:', error);
  }
}

// 加载文件列表
async function loadFiles() {
  try {
    const params = {
      searchKey: AppState.searchKey,
      selectedLibrary: AppState.selectedLibrary,
      filterStatus: AppState.filterStatus,
      startDate: document.getElementById('startDate')?.value,
      endDate: document.getElementById('endDate')?.value
    };

    const response = await ApiClient.get('/getCollectionListInfo/', params);
    if (response.code === 200) {
      AppState.files = response.data;
      AppState.filteredFiles = response.data;
      renderFiles();
    }
  } catch (error) {
    console.error('加载文件列表失败:', error);
    showError('加载文件列表失败');
  }
}

// 更新知识库下拉选择框
function updateLibrarySelect() {
  const select = document.getElementById('selectedLibrary');
  if (!select) return;

  // 清空现有选项（保留第一个默认选项）
  select.innerHTML = '<option value="">选择知识库</option>';

  // 添加知识库选项
  AppState.libraries.forEach(library => {
    const option = document.createElement('option');
    option.value = library.id;
    option.textContent = library.name;
    select.appendChild(option);
  });
}

// 渲染文件列表
function renderFiles() {
  const container = document.getElementById('filesContainer');
  const emptyState = document.getElementById('emptyState');

  if (!container) return;

  // 清空容器
  container.innerHTML = '';

  if (AppState.filteredFiles.length === 0) {
    // 显示空状态
    emptyState?.classList.remove('d-none');
    return;
  }

  // 隐藏空状态
  emptyState?.classList.add('d-none');

  // 渲染文件卡片
  AppState.filteredFiles.forEach((file, index) => {
    const cardHtml = createFileCard(file, index);
    container.insertAdjacentHTML('beforeend', cardHtml);
  });

  // 初始化工具提示
  initTooltips();
}

// 创建文件卡片HTML
function createFileCard(file, index) {
  const isAudited = file.audit == 1;
  const showAuditBtn = AppState.sfilesh && file.audit == 0;
  const showDeleteBtn = file.audit == 0;

  return `
    <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6">
      <div class="card file-card h-100" data-file-index="${index}">
        <div class="file-header d-none" style="font-size: 10px;">
          <span class="dataset-id">${file.datasetId}</span> <span class="file-id">${file.id}</span>
        </div>

        <div class="position-relative">
          <div class="status-badge position-absolute top-0 end-0 m-2">
            ${isAudited ?
              '<span class="badge bg-success"><i class="bi bi-check-circle me-1"></i>已审核</span>' :
              '<span class="badge bg-warning"><i class="bi bi-exclamation-circle me-1"></i>未审核</span>'
            }
          </div>
        </div>

        <div class="card-body text-center">
          <i class="bi bi-file-earmark-text text-primary" style="font-size: 36px;"></i>

          <div class="file-name mt-2 fw-bold"
               data-bs-toggle="tooltip"
               data-bs-placement="top"
               title="${file.name}">
            ${truncateText(file.name, 20)}
          </div>

          <div class="file-name2 text-muted small">${file.datasetname || ''}</div>

          <div class="file-meta mt-2 small text-muted">
            <div class="file-size">${formatFileSize(file.size)}</div>
            <div class="file-time">${formatTime(file.time)}</div>
          </div>

          <div class="mt-3">
            <button type="button"
                    class="btn btn-link btn-sm p-0 me-2"
                    onclick="previewFile(${index})">
              预览
            </button>
            ${showAuditBtn ?
              `<button type="button"
                       class="btn btn-link btn-sm p-0 me-2"
                       onclick="auditFile(${index})">
                 审核
               </button>` : ''
            }
            ${showDeleteBtn ?
              `<button type="button"
                       class="btn btn-link btn-sm p-0 text-danger"
                       onclick="deleteFile(${index})">
                 删除
               </button>` : ''
            }
          </div>
        </div>
      </div>
    </div>
  `;
}

// 初始化工具提示
function initTooltips() {
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new Bootstrap.Tooltip(tooltipTriggerEl);
  });
}

// ==================== 事件处理函数 ====================

// 菜单选择处理
function handleMenuSelect(menuKey) {
  AppState.activeMenu = menuKey;

  // 更新菜单样式
  document.querySelectorAll('.nav-link').forEach(link => {
    link.classList.remove('active');
  });

  const activeLink = document.querySelector(`[data-menu="${menuKey}"]`);
  if (activeLink) {
    activeLink.classList.add('active');
  }

  // 根据菜单切换页面内容
  if (window.router) {
    window.router.navigate(menuKey);
  }
}

// 搜索处理
function handleSearch(value) {
  AppState.searchKey = value;
  filterFiles();
}

// 日期范围变化处理
function handleDateChange() {
  const startDate = document.getElementById('startDate')?.value;
  const endDate = document.getElementById('endDate')?.value;
  AppState.dateRange = [startDate, endDate];
  filterFiles();
}

// 知识库选择处理
function handleLibraryChange(value) {
  AppState.selectedLibrary = value;
  filterFiles();
}

// 状态过滤处理
function handleStatusChange(value) {
  AppState.filterStatus = value;
  filterFiles();
}

// 查询按钮处理
function handleQuery() {
  loadFiles();
}

// 导入数据按钮处理
function handleOpenImport() {
  showModal('uploadModal');
}

// 登录按钮处理
function handleLoginClick(action) {
  if (action === 'logon') {
    showModal('loginModal');
  } else if (action === 'exit') {
    logout();
  }
}

// 文件过滤
function filterFiles() {
  let filtered = [...AppState.files];

  // 搜索关键词过滤
  if (AppState.searchKey) {
    const keyword = AppState.searchKey.toLowerCase();
    filtered = filtered.filter(file =>
      file.name.toLowerCase().includes(keyword) ||
      (file.datasetname && file.datasetname.toLowerCase().includes(keyword))
    );
  }

  // 知识库过滤
  if (AppState.selectedLibrary) {
    filtered = filtered.filter(file => file.datasetId === AppState.selectedLibrary);
  }

  // 状态过滤
  if (AppState.filterStatus !== '') {
    filtered = filtered.filter(file => file.audit == AppState.filterStatus);
  }

  // 日期范围过滤
  const [startDate, endDate] = AppState.dateRange;
  if (startDate) {
    filtered = filtered.filter(file => new Date(file.time) >= new Date(startDate));
  }
  if (endDate) {
    filtered = filtered.filter(file => new Date(file.time) <= new Date(endDate + ' 23:59:59'));
  }

  AppState.filteredFiles = filtered;
  renderFiles();
}

// ==================== 文件操作函数 ====================

// 预览文件
function previewFile(index) {
  const file = AppState.filteredFiles[index];
  if (!file) return;

  AppState.currentFile = file;

  // 加载文件详细信息
  loadFileDetails(file.id).then(() => {
    showModal('previewModal');
  });
}

// 审核文件
function auditFile(index) {
  const file = AppState.filteredFiles[index];
  if (!file) return;

  AppState.currentFile = file;

  // 加载相同型号文件
  loadSameNameFiles(file.name).then(() => {
    showModal('auditModal');
  });
}

// 删除文件
function deleteFile(index) {
  const file = AppState.filteredFiles[index];
  if (!file) return;

  AppState.currentFile = file;

  // 设置删除对话框内容
  const deleteFileName = document.getElementById('deleteFileName');
  if (deleteFileName) {
    deleteFileName.textContent = file.name;
  }

  showModal('deleteModal');
}

// 确认删除
async function confirmDelete() {
  if (!AppState.currentFile) return;

  const deleteBtn = document.getElementById('confirmDeleteBtn');
  const spinner = document.getElementById('deleteSpinner');

  try {
    // 显示加载状态
    deleteBtn.disabled = true;
    spinner?.classList.remove('d-none');

    const response = await ApiClient.get('/deleteCollection/', {
      collectionId: AppState.currentFile.id
    });

    if (response.code === 200) {
      showSuccess('删除成功');
      hideModal('deleteModal');
      loadFiles(); // 重新加载文件列表
    } else {
      showError(response.message || '删除失败');
    }
  } catch (error) {
    console.error('删除文件失败:', error);
    showError('删除失败');
  } finally {
    deleteBtn.disabled = false;
    spinner?.classList.add('d-none');
  }
}

// 确认审核
async function confirmAudit() {
  if (!AppState.currentFile) return;

  const auditBtn = document.getElementById('confirmAuditBtn');
  const spinner = document.getElementById('auditSpinner');

  try {
    // 显示加载状态
    auditBtn.disabled = true;
    spinner?.classList.remove('d-none');

    const response = await ApiClient.post('/auditCollection/', {
      collectionId: AppState.currentFile.id
    });

    if (response.code === 200) {
      showSuccess('审核成功');
      hideModal('auditModal');
      loadFiles(); // 重新加载文件列表
    } else {
      showError(response.message || '审核失败');
    }
  } catch (error) {
    console.error('审核失败:', error);
    showError('审核失败');
  } finally {
    auditBtn.disabled = false;
    spinner?.classList.add('d-none');
  }
}

// 加载文件详细信息
async function loadFileDetails(fileId) {
  try {
    const response = await ApiClient.get('/getDatasetdatas/', {
      collectionId: fileId
    });

    if (response.code === 200) {
      AppState.previewData = response.data;
      updatePreviewModal();
    }
  } catch (error) {
    console.error('加载文件详情失败:', error);
    showError('加载文件详情失败');
  }
}

// 加载相同型号文件
async function loadSameNameFiles(fileName) {
  try {
    const response = await ApiClient.get('/getSameNameFiles/', {
      fileName: fileName
    });

    if (response.code === 200) {
      AppState.sameFiles = response.data;
      updateSameFilesTable();
    }
  } catch (error) {
    console.error('加载相同型号文件失败:', error);
    showError('加载相同型号文件失败');
  }
}

// ==================== 工具函数 ====================

// 更新预览模态框
function updatePreviewModal() {
  // 这里需要根据实际的预览模态框结构来实现
  console.log('更新预览模态框:', AppState.previewData);
}

// 更新相同文件表格
function updateSameFilesTable() {
  const tableBody = document.getElementById('sameFilesTable');
  if (!tableBody) return;

  tableBody.innerHTML = '';

  AppState.sameFiles.forEach(file => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>${file.name}</td>
      <td>${formatTime(file.time)}</td>
      <td>
        <span class="badge ${file.audit === '1' ? 'bg-success' : 'bg-warning'}">
          ${file.audit === '1' ? '已审核' : '未审核'}
        </span>
      </td>
    `;
    tableBody.appendChild(row);
  });
}

// 检查登录状态
function checkLoginStatus() {
  const token = StorageManager.get('token');
  if (token) {
    AppState.token = token;
    AppState.sfilesh = true;
    updateLoginButton();
    showAuditMenu();
  }
}

// 更新登录按钮
function updateLoginButton() {
  const loginBtn = document.getElementById('loginBtn');
  if (!loginBtn) return;

  if (AppState.sfilesh) {
    loginBtn.innerHTML = '<i class="bi bi-person me-1"></i>退出';
    loginBtn.className = 'btn btn-outline-danger';
    loginBtn.onclick = () => handleLoginClick('exit');
  } else {
    loginBtn.innerHTML = '<i class="bi bi-person me-1"></i>登录';
    loginBtn.className = 'btn btn-outline-secondary';
    loginBtn.onclick = () => handleLoginClick('logon');
  }
}

// 显示审核菜单
function showAuditMenu() {
  const auditMenu = document.querySelector('.audit-menu');
  if (auditMenu) {
    auditMenu.style.display = AppState.sfilesh ? 'block' : 'none';
  }
}

// 退出登录
function logout() {
  AppState.sfilesh = false;
  AppState.token = '';
  StorageManager.remove('token');
  updateLoginButton();
  showAuditMenu();
  showSuccess('已退出登录');
}

// 更新当前时间
function updateCurrentTime() {
  const timeElement = document.getElementById('currentTime');
  if (timeElement) {
    timeElement.textContent = formatTime(new Date());
  }
}

// 显示加载状态
function showLoadingState() {
  const loadingState = document.getElementById('loadingState');
  const filesContainer = document.getElementById('filesContainer');
  const emptyState = document.getElementById('emptyState');

  loadingState?.classList.remove('d-none');
  filesContainer?.classList.add('d-none');
  emptyState?.classList.add('d-none');
}

// 隐藏加载状态
function hideLoadingState() {
  const loadingState = document.getElementById('loadingState');
  const filesContainer = document.getElementById('filesContainer');

  loadingState?.classList.add('d-none');
  filesContainer?.classList.remove('d-none');
}

// 确认文件信息
function confirmFileInfo() {
  const form = document.getElementById('fileInfoForm');
  if (!form) return;

  const formData = new FormData(form);
  const fileInfo = {};

  for (let [key, value] of formData.entries()) {
    fileInfo[key] = value;
  }

  console.log('文件信息:', fileInfo);
  hideModal('fileInfoModal');
  showSuccess('文件信息已保存');
}

// ==================== 登录相关函数 ====================

// 处理登录
async function handleLogin() {
  const form = document.getElementById('loginForm');
  const submitBtn = document.getElementById('loginSubmitBtn');
  const spinner = document.getElementById('loginSpinner');

  if (!form.checkValidity()) {
    form.classList.add('was-validated');
    return;
  }

  const formData = new FormData(form);
  const loginData = {
    username: formData.get('username'),
    password: formData.get('password')
  };

  try {
    submitBtn.disabled = true;
    spinner?.classList.remove('d-none');

    const response = await ApiClient.post('/login/', loginData);

    if (response.code === 200) {
      AppState.sfilesh = true;
      AppState.token = response.data.token;
      StorageManager.set('token', response.data.token);

      hideModal('loginModal');
      updateLoginButton();
      showAuditMenu();
      showSuccess('登录成功');

      // 重置表单
      form.reset();
      form.classList.remove('was-validated');
    } else {
      showError(response.message || '登录失败');
    }
  } catch (error) {
    console.error('登录失败:', error);
    showError('登录失败');
  } finally {
    submitBtn.disabled = false;
    spinner?.classList.add('d-none');
  }
}

// 处理登录回车键
function handleLoginKeyPress(event) {
  if (event.key === 'Enter') {
    handleLogin();
  }
}

// ==================== 上传相关函数 ====================

// 切换自定义参数显示
function toggleCustomParams() {
  const customParam = document.getElementById('customParam');
  const customSection = document.getElementById('customParamsSection');

  if (customParam && customParam.checked) {
    customSection?.classList.remove('d-none');
  } else {
    customSection?.classList.add('d-none');
  }
}

// 处理文件拖拽
function handleDragOver(event) {
  event.preventDefault();
  event.currentTarget.classList.add('border-primary');
}

function handleDragLeave(event) {
  event.preventDefault();
  event.currentTarget.classList.remove('border-primary');
}

function handleFileDrop(event) {
  event.preventDefault();
  event.currentTarget.classList.remove('border-primary');

  const files = event.dataTransfer.files;
  handleFiles(files);
}

// 处理文件选择
function handleFileSelect(event) {
  const files = event.target.files;
  handleFiles(files);
}

// 处理文件
function handleFiles(files) {
  AppState.selectedFiles = Array.from(files);
  updateFileList();
  updateUploadButton();
}

// 更新文件列表显示
function updateFileList() {
  const fileList = document.getElementById('fileList');
  if (!fileList) return;

  if (AppState.selectedFiles.length === 0) {
    fileList.innerHTML = '';
    return;
  }

  const listHtml = AppState.selectedFiles.map((file, index) => `
    <div class="d-flex justify-content-between align-items-center border rounded p-2 mb-2">
      <div>
        <i class="bi bi-file-earmark me-2"></i>
        <span>${file.name}</span>
        <small class="text-muted ms-2">(${formatFileSize(file.size)})</small>
      </div>
      <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
        <i class="bi bi-x"></i>
      </button>
    </div>
  `).join('');

  fileList.innerHTML = listHtml;
}

// 移除文件
function removeFile(index) {
  AppState.selectedFiles.splice(index, 1);
  updateFileList();
  updateUploadButton();
}

// 更新上传按钮状态
function updateUploadButton() {
  const uploadBtn = document.getElementById('uploadSubmitBtn');
  if (uploadBtn) {
    uploadBtn.disabled = AppState.selectedFiles.length === 0;
  }
}

// 重置上传表单
function resetUploadForm() {
  const form = document.getElementById('uploadForm');
  if (form) {
    form.reset();
    form.classList.remove('was-validated');
  }

  AppState.selectedFiles = [];
  updateFileList();
  updateUploadButton();

  // 隐藏进度条
  const progressSection = document.getElementById('uploadProgressSection');
  progressSection?.classList.add('d-none');
}

// 上传所有文件
async function uploadAllFiles() {
  const form = document.getElementById('uploadForm');
  const submitBtn = document.getElementById('uploadSubmitBtn');
  const spinner = document.getElementById('uploadSpinner');
  const progressSection = document.getElementById('uploadProgressSection');
  const progressBar = document.getElementById('uploadProgressBar');
  const progressText = document.getElementById('uploadProgressText');

  if (!form.checkValidity()) {
    form.classList.add('was-validated');
    return;
  }

  if (AppState.selectedFiles.length === 0) {
    showError('请选择要上传的文件');
    return;
  }

  try {
    submitBtn.disabled = true;
    spinner?.classList.remove('d-none');
    progressSection?.classList.remove('d-none');

    const formData = new FormData(form);

    // 添加文件到FormData
    AppState.selectedFiles.forEach(file => {
      formData.append('files', file);
    });

    const response = await ApiClient.uploadFiles('/uploadfiles/', formData, (progress) => {
      if (progressBar) {
        progressBar.style.width = progress + '%';
        progressBar.setAttribute('aria-valuenow', progress);
      }
      if (progressText) {
        progressText.textContent = `上传进度: ${progress}%`;
      }
    });

    if (response.code === 200) {
      showSuccess('上传成功');
      hideModal('uploadModal');
      resetUploadForm();
      loadFiles(); // 重新加载文件列表
    } else {
      showError(response.message || '上传失败');
    }
  } catch (error) {
    console.error('上传失败:', error);
    showError('上传失败');
  } finally {
    submitBtn.disabled = false;
    spinner?.classList.add('d-none');
  }
}

// 导出全局函数供HTML调用
window.handleMenuSelect = handleMenuSelect;
window.handleSearch = handleSearch;
window.handleLibraryChange = handleLibraryChange;
window.handleStatusChange = handleStatusChange;
window.handleQuery = handleQuery;
window.handleOpenImport = handleOpenImport;
window.handleLoginClick = handleLoginClick;
window.previewFile = previewFile;
window.auditFile = auditFile;
window.deleteFile = deleteFile;
window.confirmDelete = confirmDelete;
window.confirmAudit = confirmAudit;
window.confirmFileInfo = confirmFileInfo;
window.handleLogin = handleLogin;
window.handleLoginKeyPress = handleLoginKeyPress;
window.toggleCustomParams = toggleCustomParams;
window.handleDragOver = handleDragOver;
window.handleDragLeave = handleDragLeave;
window.handleFileDrop = handleFileDrop;
window.handleFileSelect = handleFileSelect;
window.removeFile = removeFile;
window.resetUploadForm = resetUploadForm;
window.uploadAllFiles = uploadAllFiles;
        
        // 日期选择器配置
        pickerOptions: {
          shortcuts: [{
            text: '本周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setDate(start.getDate() - start.getDay() + 1);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '本月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setDate(1);
              picker.$emit('pick', [start, end]);
            }
          }]
        }
      };
    },
    
    computed: {
      // 计算属性可以在这里添加
    },
    
    mounted() {
      this.initializeApp();
    },
    
    methods: {
      /**
       * 初始化应用
       */
      async initializeApp() {
        // 初始化认证状态
        const authState = AuthManager.initAuthState();
        this.sfilesh = authState.hasPermission;
        this.token = authState.token;
        
        // 加载数据
        await this.loadLibraries();
        await this.loadFiles();
      },
      
      /**
       * 加载知识库列表
       */
      async loadLibraries() {
        try {
          const response = await DataManager.getDatasetsList();
          if (response.code === 200) {
            this.libraries = response.data;
          }
        } catch (error) {
          console.error('加载知识库列表失败:', error);
        }
      },
      
      /**
       * 加载文件列表
       */
      async loadFiles(params = {}) {
        this.loading = true;
        try {
          const response = await DataManager.getCollectionListInfo(params);
          if (response.code === 200) {
            this.files = response.data;
            this.filteredFiles = this.files;
            this.serverIP = response.serverIP || '';
          }
        } catch (error) {
          console.error('加载文件失败:', error);
          this.$showError('加载文件失败');
        } finally {
          this.loading = false;
        }
      },
      
      /**
       * 处理菜单选择
       */
      handleMenuSelect(index) {
        if (index === '2') {
          this.loadFiles();
          this.activeMenu = index;
        } else if (index === '1') {
          window.open(`http://${this.serverIP}/h5_log/`, '_blank');
        } else if (index === '3') {
          window.open(`http://${this.serverIP}/h5_images/`, '_blank');
        } else if (index === '4') {
          window.open(`http://${this.serverIP}/h5_images_audit_wh/`, '_blank');
        }
      },
      
      /**
       * 处理搜索
       */
      handleSearch() {
        this.filteredFiles = this.files.filter(file =>
          file.name.toLowerCase().includes(this.searchKey.toLowerCase())
        );
      },
      
      /**
       * 处理查询
       */
      handleQuery() {
        const params = {
          keyword: this.searchKey,
          startDate: this.dateRange[0] ? dayjs(this.dateRange[0]).format('YYYY-MM-DD') : '',
          endDate: this.dateRange[1] ? dayjs(this.dateRange[1]).format('YYYY-MM-DD') : '',
          status: this.filterStatus,
          dataset: this.selectedLibrary,
        };
        console.log('查询参数:', params);
        this.loadFiles(params);
      },
      
      /**
       * 处理登录点击
       */
      handleLoginClick(action) {
        if (action === 'logon') {
          this.loginDialogVisible = true;
        } else {
          AuthManager.logout();
          this.sfilesh = false;
          this.token = '';
          this.loadFiles();
        }
      },
      
      /**
       * 处理登录
       */
      async handleLogin() {
        if (!AuthManager.validateLoginForm(this.loginForm.username, this.loginForm.password)) {
          return;
        }
        
        this.loginLoading = true;
        try {
          const response = await AuthManager.login(this.loginForm.username, this.loginForm.password);
          
          if (response.code === 200) {
            this.$showSuccess('登录成功');
            this.sfilesh = true;
            this.token = response.token;
            this.loginDialogVisible = false;
            
            // 保存认证状态
            AuthManager.setToken(response.token);
            AuthManager.setPermissionStatus(true);
            
            this.loadFiles();
          } else {
            this.$showError('登录失败：' + response.message);
          }
        } catch (error) {
          console.error('登录失败:', error);
          this.$showError('登录失败');
        } finally {
          this.loginLoading = false;
        }
      },
      
      /**
       * 打开导入对话框
       */
      handleOpenImport() {
        this.importDialogVisible = true;
      },
      
      /**
       * 重置上传表单
       */
      resetUploadForm() {
        this.uploadForm = {
          selectedLibrary: '',
          processingMethod: 'chunk',
          processingParam: 'auto',
          chunkLength: 512,
          customSeparator: ''
        };
        this.selectedFiles = [];
        this.uploadProgress = 0;
        this.uploading = false;
      },
      
      /**
       * 处理文件变化
       */
      handleFileChange(file, fileList) {
        this.selectedFiles = fileList;
      },
      
      /**
       * 处理文件移除
       */
      handleRemove(file, fileList) {
        this.selectedFiles = fileList;
      },

      /**
       * 上传所有文件
       */
      async uploadAllFiles() {
        if (this.selectedFiles.length === 0) {
          this.$showWarning('请先选择文件');
          return;
        }

        if (!FileManager.validateBeforeUpload(
          null,
          this.uploadForm.selectedLibrary,
          this.uploadForm.processingParam,
          this.uploadForm.processingMethod,
          this.uploadForm.chunkLength
        )) {
          return;
        }

        this.uploading = true;
        this.uploadProgress = 0;

        try {
          const processParams = FileManager.buildProcessParams(
            this.uploadForm.processingMethod,
            this.uploadForm.processingParam,
            this.uploadForm.chunkLength,
            this.uploadForm.customSeparator
          );

          let completed = 0;
          const total = this.selectedFiles.length;

          for (const file of this.selectedFiles) {
            try {
              await FileManager.uploadFile(
                file.raw || file,
                this.uploadForm.selectedLibrary,
                processParams,
                (progress) => {
                  // 单个文件进度更新
                  const overallProgress = Math.round(((completed + progress / 100) / total) * 100);
                  this.uploadProgress = overallProgress;
                }
              );

              completed++;
              this.uploadProgress = Math.round((completed / total) * 100);

            } catch (error) {
              console.error(`文件 ${file.name} 上传失败:`, error);
              this.$showError(`文件 ${file.name} 上传失败`);
            }
          }

          if (completed === total) {
            this.$showSuccess('所有文件上传成功');
            this.importDialogVisible = false;
            this.resetUploadForm();
            this.loadFiles();
          } else {
            this.$showWarning(`${completed}/${total} 个文件上传成功`);
          }

        } catch (error) {
          console.error('批量上传失败:', error);
          this.$showError('批量上传失败');
        } finally {
          this.uploading = false;
        }
      },

      /**
       * 预览文件
       */
      async previewFile(file) {
        this.loading = true;
        try {
          const response = await DataManager.getDatasetDatas(file.id);
          if (response.code === 200) {
            this.previewData = response.data;
            this.previewDialogVisible = true;
          }
        } catch (error) {
          console.error('预览文件失败:', error);
          this.$showError('预览文件失败');
        } finally {
          this.loading = false;
        }
      },

      /**
       * 删除文件
       */
      deleteFile(file) {
        this.currentFile = file;
        this.deleteDialogVisible = true;
      },

      /**
       * 确认删除
       */
      async confirmDelete() {
        this.loading = true;
        try {
          const response = await DataManager.deleteCollection(this.currentFile.id);
          if (response.code === 200) {
            this.$showSuccess('删除成功');
            this.loadFiles();
          } else {
            this.$showError('删除失败');
          }
        } catch (error) {
          console.error('删除文件失败:', error);
          this.$showError('删除文件失败');
        } finally {
          this.loading = false;
          this.deleteDialogVisible = false;
        }
      },

      /**
       * 审核文件
       */
      async auditFile(file) {
        this.currentFile = file;

        // 获取相同型号的文件
        try {
          const response = await DataManager.getSameNameFiles(file.cpxh, file.id);
          if (response.code === 200) {
            this.sameFiles = response.data;
            this.auditDialogVisible = true;
          }
        } catch (error) {
          console.error('获取相同型号文件失败:', error);
          this.$showError('获取相同型号文件失败');
        }
      },

      /**
       * 确认审核
       */
      async confirmAudit() {
        this.auditing = true;
        try {
          const response = await ApiClient.post('/auditCollection/', {
            collectionId: this.currentFile.id,
            token: this.token
          });

          if (response.code === 200) {
            this.$showSuccess('审核成功');
            this.auditDialogVisible = false;
            this.loadFiles();
          } else {
            this.$showError('审核失败：' + response.message);
          }
        } catch (error) {
          console.error('审核失败:', error);
          this.$showError('审核失败');
        } finally {
          this.auditing = false;
        }
      },

      /**
       * 保存预览数据
       */
      async savePreviewData() {
        this.saving = true;
        try {
          // 这里需要实现保存逻辑
          this.$showSuccess('保存成功');
          this.previewDialogVisible = false;
        } catch (error) {
          console.error('保存失败:', error);
          this.$showError('保存失败');
        } finally {
          this.saving = false;
        }
      },

      /**
       * 添加新索引
       */
      addNewIndex(indexes) {
        indexes.push({
          text: '',
          editing: true
        });
      },

      /**
       * 删除QA项目
       */
      async deleteQAItem(id, index) {
        try {
          await this.$confirm('确定要删除这个QA项目吗？', '确认删除');

          const response = await DataManager.deleteQA(id);
          if (response.code === 200) {
            this.previewData.q.splice(index, 1);
            this.$showSuccess('删除成功');
          }
        } catch (error) {
          if (error !== 'cancel') {
            console.error('删除QA项目失败:', error);
            this.$showError('删除失败');
          }
        }
      },

      /**
       * 格式化时间
       */
      formatTime(row, column, cellValue) {
        return dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss');
      }
    }
  });
}

// 导出初始化函数
window.initVueApp = initVueApp;
