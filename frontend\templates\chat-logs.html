{% extends "base.html" %}

{% block title %}对话日志管理{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/frontend/static/css/chat-logs.css">
{% endblock %}

{% block content %}
<div class="main-container d-flex">
  <!-- 侧边栏导航 -->
  {% include 'components/layout/sidebar.html' %}

  <!-- 右侧内容 -->
  <div class="main-content flex-grow-1">
    <!-- 顶部工具栏 -->
    <div class="toolbar d-flex align-items-center gap-2 p-3 bg-white border-bottom">
      <select class="form-select" id="selectedApp" style="width: 200px;" onchange="handleAppChange(this.value)">
        <option value="">选择应用</option>
        <!-- 选项将通过JavaScript动态添加 -->
      </select>

      <div class="d-flex gap-2">
        <input type="date"
               class="form-control"
               id="startDate"
               style="width: 150px;">
        <span class="align-self-center">至</span>
        <input type="date"
               class="form-control"
               id="endDate"
               style="width: 150px;">
      </div>

      <button type="button" class="btn btn-primary" onclick="queryLogs()">
        <span class="spinner-border spinner-border-sm d-none me-1" id="querySpinner"></span>
        查询
      </button>
      <button type="button" class="btn btn-success" onclick="exportData()">
        <span class="spinner-border spinner-border-sm d-none me-1" id="exportSpinner"></span>
        导出
      </button>
    </div>

    <!-- 数据表格 -->
    <div class="table-container p-3">
      <div class="table-responsive">
        <table class="table table-striped table-hover">
          <thead class="table-dark">
            <tr>
              <th style="width: 200px;">对话ID</th>
              <th style="width: 180px;">时间</th>
              <th style="min-width: 200px;">用户输入</th>
              <th style="min-width: 200px;">助手回复</th>
              <th style="min-width: 150px;">业务信息</th>
              <th style="width: 120px;">操作</th>
            </tr>
          </thead>
          <tbody id="chatLogsTableBody">
            <!-- 数据将通过JavaScript动态生成 -->
          </tbody>
        </table>
      </div>

      <!-- 加载状态 -->
      <div id="tableLoadingState" class="text-center py-5 d-none">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="text-muted mt-3">加载中...</p>
      </div>

      <!-- 空状态 -->
      <div id="tableEmptyState" class="text-center py-5 d-none">
        <i class="bi bi-chat-dots text-muted" style="font-size: 64px;"></i>
        <p class="text-muted mt-3">暂无对话记录</p>
      </div>

      <!-- 分页 -->
      <nav aria-label="分页导航" class="mt-4">
        <div class="d-flex justify-content-between align-items-center">
          <div class="text-muted">
            共 <span id="totalRecords">0</span> 条记录
          </div>
          <ul class="pagination mb-0" id="pagination">
            <!-- 分页按钮将通过JavaScript动态生成 -->
          </ul>
        </div>
      </nav>
    </div>
  </div>
</div>

<!-- 详情对话框 -->
<div class="modal fade" id="detailModal" tabindex="-1">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">对话详情</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="detail-content">
          <h6>基本信息</h6>
          <div class="row">
            <div class="col-md-6">
              <div class="mb-2">
                <strong>对话ID：</strong>
                <span id="detailChatId"></span>
              </div>
              <div class="mb-2">
                <strong>应用：</strong>
                <span id="detailAppName"></span>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-2">
                <strong>时间：</strong>
                <span id="detailTime"></span>
              </div>
              <div class="mb-2">
                <strong>用户：</strong>
                <span id="detailUser"></span>
              </div>
            </div>
          </div>

          <hr>
          <h6>对话内容</h6>

          <div class="chat-content">
            <div class="user-message mb-3">
              <div class="fw-bold text-primary mb-2">用户输入：</div>
              <div class="border rounded p-3 bg-light" id="detailUserInput"></div>
            </div>

            <div class="assistant-message mb-3">
              <div class="fw-bold text-success mb-2">助手回复：</div>
              <div class="border rounded p-3 bg-light" id="detailAssistantOutput"></div>
            </div>

            <div class="business-info mb-3" id="detailBusinessInfo" style="display: none;">
              <div class="fw-bold text-info mb-2">业务信息：</div>
              <div class="border rounded p-3 bg-light" id="detailBusinessContent"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
      </div>
    </div>
  </div>
</div>

<!-- 反馈对话框 -->
<div class="modal fade" id="feedbackModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">保存反馈</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="feedbackForm">
          <div class="mb-3">
            <label for="feedbackChatId" class="form-label">对话ID</label>
            <input type="text" class="form-control" id="feedbackChatId" name="chatId" readonly>
          </div>

          <div class="mb-3">
            <label for="feedbackType" class="form-label">反馈类型</label>
            <select class="form-select" id="feedbackType" name="type" required>
              <option value="">请选择反馈类型</option>
              <option value="satisfied">满意</option>
              <option value="unsatisfied">不满意</option>
              <option value="error">错误回复</option>
              <option value="other">其他</option>
            </select>
            <div class="invalid-feedback">
              请选择反馈类型
            </div>
          </div>

          <div class="mb-3">
            <label for="feedbackContent" class="form-label">反馈内容</label>
            <textarea class="form-control"
                      id="feedbackContent"
                      name="content"
                      rows="4"
                      placeholder="请输入反馈内容"
                      required></textarea>
            <div class="invalid-feedback">
              请输入反馈内容
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" id="submitFeedbackBtn" onclick="submitFeedback()">
          <span class="spinner-border spinner-border-sm d-none me-1" id="feedbackSpinner"></span>
          提交
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="/frontend/static/js/chat-logs.js"></script>
{% endblock %}
