"""
API路由配置
重新组织的模块化路由结构
"""

from fastapi import APIRouter

# 导入各模块的处理器
from core.knowledge_base.handlers import (
    index, get_datasets_list, get_collection_list_info,
    get_same_name_files, get_dataset_datas
)
from core.file_management.handlers import (
    get_file_info, upload_files, delete_collection, save_biaoshu
)
from core.audit.handlers import (
    audit_collection, update_dataset_datas, delete_qa
)
from core.auth.handlers import login

# 导入新的模块化处理器
from core.image_management.handlers import (
    get_collections_list as getcollectionsList,
    get_datasets_datas_images as getDatasetsDatas_images,
    upload_image as uploadimage,
    audit_image as whauditimage,
    h5_images_page as h5_images,
    h5_images_audit_page as h5_images_audit_wh
)

from core.chat_logs.handlers import (
    get_app_list as applist,
    get_chat_logs as getChatLogs,
    save_feedbacks as saveFeedbacks,
    get_pagination_records as getPaginationRecords,
    h5_log_page as h5_log
)

# 导入调度器服务
from utils.scheduler import sync_indexs

# 创建主路由器
router = APIRouter()

# ==================== 知识库相关路由 ====================
router.add_api_route("/", index, methods=["GET"], tags=["知识库"])
router.add_api_route("/index/", index, methods=["GET"], tags=["知识库"])
router.add_api_route("/getDatasList/", get_datasets_list, methods=["GET"], tags=["知识库"])
router.add_api_route("/getCollectionListInfo/", get_collection_list_info, methods=["GET"], tags=["知识库"])
router.add_api_route("/getSameNameFiles/", get_same_name_files, methods=["GET"], tags=["知识库"])
router.add_api_route("/getDatasetdatas/", get_dataset_datas, methods=["GET"], tags=["知识库"])

# ==================== 文件管理相关路由 ====================
router.add_api_route("/getFileInfo/", get_file_info, methods=["GET"], tags=["文件管理"])
router.add_api_route("/uploadfiles/", upload_files, methods=["POST"], tags=["文件管理"])
router.add_api_route("/deleteCollection/", delete_collection, methods=["GET"], tags=["文件管理"])
router.add_api_route("/savebs/", save_biaoshu, methods=["POST"], tags=["文件管理"])

# ==================== 审核相关路由 ====================
router.add_api_route("/auditCollection/", audit_collection, methods=["POST"], tags=["审核"])
router.add_api_route("/updateDatasetdatas/", update_dataset_datas, methods=["POST"], tags=["审核"])
router.add_api_route("/deleteQA/", delete_qa, methods=["POST"], tags=["审核"])

# ==================== 认证相关路由 ====================
router.add_api_route("/login/", login, methods=["POST"], tags=["认证"])

# ==================== 图片管理相关路由（保持兼容性）====================
router.add_api_route("/h5_images/", h5_images, methods=["GET"], tags=["图片管理"])
router.add_api_route("/h5_images_audit_wh/", h5_images_audit_wh, methods=["GET"], tags=["图片管理"])
router.add_api_route("/getcollectionsList/", getcollectionsList, methods=["GET"], tags=["图片管理"])
router.add_api_route("/getDatasetsDatas_images/", getDatasetsDatas_images, methods=["GET"], tags=["图片管理"])
router.add_api_route("/uploadimage/", uploadimage, methods=["POST"], tags=["图片管理"])
router.add_api_route("/whauditimage/", whauditimage, methods=["POST"], tags=["图片管理"])

# ==================== 日志管理相关路由（保持兼容性）====================
router.add_api_route("/applist/", applist, methods=["GET"], tags=["日志管理"])
router.add_api_route("/h5_log/", h5_log, methods=["GET"], tags=["日志管理"])
router.add_api_route("/getChatLogs/", getChatLogs, methods=["GET"], tags=["日志管理"])
router.add_api_route("/saveFeedbacks/", saveFeedbacks, methods=["POST"], tags=["日志管理"])
router.add_api_route("/getPaginationRecords/", getPaginationRecords, methods=["POST"], tags=["日志管理"])
