"""
聊天日志服务层
处理聊天日志相关的业务逻辑
"""

import datetime
from typing import Dict, List, Any
from bson.objectid import ObjectId

from database.connection import get_database


class ChatLogsService:
    """聊天日志服务类"""
    
    def get_app_list(self) -> Dict[str, Any]:
        """获取应用列表"""
        try:
            db = get_database()
            apps = db['apps'].find().sort("updateTime", 1)
            data = []
            for app in apps:
                data.append({"_id": str(app["_id"]), "name": app["name"]})
            return {"code": 200, "data": data}
        except Exception as e:
            return {"code": 500, "message": str(e)}
    
    def get_chat_logs(self, date_start: str, date_end: str, page_num: int, app_id: str) -> Dict[str, Any]:
        """获取聊天日志"""
        try:
            db = get_database()
            date_end = date_end.replace('00:00:00', '23:59:59')
            date_start = datetime.datetime.strptime(date_start, '%Y-%m-%dT%H:%M:%S.000z')
            date_end = datetime.datetime.strptime(date_end, '%Y-%m-%dT%H:%M:%S.000z')
            
            # 构建查询条件
            query = {
                "appId": ObjectId(app_id),
                "$and": [
                    {"updateTime": {"$gte": date_start}},
                    {"updateTime": {"$lte": date_end}}
                ]
            }
            
            # 执行查询并排序
            dataset_datas = db['chats'].find(query).sort("updateTime", 1)
            dataset_datas = [{"id": dd["chatId"], "time": dd["updateTime"]} for dd in dataset_datas]
            dataset_datas = {"code": 200, "data": {"total": len(dataset_datas), "data": dataset_datas}}
            return dataset_datas
        except Exception as e:
            return {"code": 500, "message": str(e)}
    
    def get_response_ywk(self, dd: Dict) -> str:
        """提取业务信息"""
        try:
            responseData = dd['responseData']
            for r in responseData:
                if r.get("moduleName") == "提取业务信息":
                    return r["customOutputs"]["result"]
        except Exception as e:
            pass
        return ""
    
    def get_pagination_records(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """获取分页记录"""
        try:
            db = get_database()
            # 构建查询条件
            date_start = data["dateStart"]
            date_end = data["dateEnd"]
            date_end = date_end.replace('00:00:00', '23:59:59')
            date_start = datetime.datetime.strptime(date_start, '%Y-%m-%dT%H:%M:%S.000z')
            date_end = datetime.datetime.strptime(date_end, '%Y-%m-%dT%H:%M:%S.000z')
            
            app_id = data["appId"]
            page_num = data.get("pageNum", 1)
            page_size = data.get("pageSize", 10)
            
            # 构建查询条件
            query = {
                "appId": ObjectId(app_id),
                "$and": [
                    {"updateTime": {"$gte": date_start}},
                    {"updateTime": {"$lte": date_end}}
                ]
            }
            
            # 计算跳过的记录数
            skip = (page_num - 1) * page_size
            
            # 执行查询并排序
            chats = db['chats'].find(query).sort("updateTime", 1).skip(skip).limit(page_size)
            total = db['chats'].count_documents(query)
            
            chat_list = []
            for chat in chats:
                ywk = self.get_response_ywk(chat)
                chat_data = {
                    "id": chat.get("chatId", ""),
                    "time": chat.get("updateTime", ""),
                    "userChatInput": chat.get("userChatInput", ""),
                    "assistantChatOutput": chat.get("assistantChatOutput", ""),
                    "ywk": ywk,
                    "appId": str(chat.get("appId", ""))
                }
                chat_list.append(chat_data)
            
            return {
                "code": 200,
                "data": {
                    "total": total,
                    "data": chat_list,
                    "pageNum": page_num,
                    "pageSize": page_size
                }
            }
        except Exception as e:
            return {"code": 500, "message": str(e)}
    
    def save_feedbacks(self, chat_id: str, feedback_type: str, content: str) -> Dict[str, Any]:
        """保存反馈"""
        try:
            db = get_database()
            
            # 构建反馈数据
            feedback_data = {
                "chatId": chat_id,
                "type": feedback_type,
                "content": content,
                "createTime": datetime.datetime.now(),
                "status": "pending"  # 待处理状态
            }
            
            # 插入反馈记录
            result = db['feedbacks'].insert_one(feedback_data)
            
            if result.inserted_id:
                return {"code": 200, "message": "反馈保存成功"}
            else:
                return {"code": 500, "message": "反馈保存失败"}
        except Exception as e:
            return {"code": 500, "message": str(e)}


# 创建服务实例
chat_logs_service = ChatLogsService()
