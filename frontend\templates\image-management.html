{% extends "base.html" %}

{% block title %}图片管理{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/frontend/static/css/image-management.css">
{% endblock %}

{% block content %}
<div class="main-container d-flex">
  <!-- 侧边栏导航 -->
  {% include 'components/layout/sidebar.html' %}

  <!-- 右侧内容 -->
  <div class="main-content flex-grow-1">
    <!-- 顶部工具栏 -->
    <div class="toolbar p-3 bg-light border-bottom">
      <div class="d-flex align-items-center gap-3">
        <select id="selectedDataset" class="form-select" style="width: 200px;" onchange="ImageManager.loadCollections()">
          <option value="">选择数据集</option>
        </select>

        <select id="selectedCollection" class="form-select" style="width: 200px;" onchange="ImageManager.loadImageData()">
          <option value="">选择集合</option>
        </select>

        <button type="button" class="btn btn-primary" onclick="ImageManager.loadImageData()" id="queryBtn">
          <span class="spinner-border spinner-border-sm d-none" id="querySpinner"></span>
          查询
        </button>
        <button type="button" class="btn btn-success" onclick="ImageManager.showUploadDialog()" id="uploadBtn">
          上传图片
        </button>
      </div>
    </div>

    <!-- 图片数据展示 -->
    <div class="content-area p-3">
      <!-- 空状态 -->
      <div id="emptyState" class="empty-state text-center py-5 d-none">
        <i class="bi bi-image" style="font-size: 4rem; color: #6c757d;"></i>
        <p class="mt-3 text-muted">暂无图片数据</p>
      </div>

      <!-- 图片数据列表 -->
      <div id="imageDataContainer" class="image-data-list">
        <div id="imageDataList" class="row g-3">
          <!-- 数据项将通过JavaScript动态生成 -->
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 编辑数据对话框 -->
<div class="modal fade" id="editModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">编辑数据</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="editForm">
          <div class="mb-3">
            <label for="editQ" class="form-label">问题内容</label>
            <textarea class="form-control" id="editQ" rows="6" placeholder="请输入问题内容"></textarea>
          </div>

          <div class="mb-3">
            <label for="editA" class="form-label">答案内容</label>
            <textarea class="form-control" id="editA" rows="4" placeholder="请输入答案内容"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="ImageManager.saveEdit()" id="saveEditBtn">
          <span class="spinner-border spinner-border-sm d-none" id="saveEditSpinner"></span>
          保存
        </button>
      </div>
    </div>
  </div>
</div>

<!-- 图片预览对话框 -->
<div class="modal fade" id="previewModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">图片预览</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body text-center">
        <img id="previewImage" src="" class="img-fluid" style="max-height: 70vh;">
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
      </div>
    </div>
  </div>
</div>

<!-- 上传图片对话框 -->
<div class="modal fade" id="uploadModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">上传图片</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="upload-area border border-2 border-dashed rounded p-4 text-center"
             id="uploadArea"
             ondrop="ImageManager.handleDrop(event)"
             ondragover="ImageManager.handleDragOver(event)"
             ondragleave="ImageManager.handleDragLeave(event)">
          <i class="bi bi-cloud-upload" style="font-size: 3rem; color: #6c757d;"></i>
          <div class="mt-3">
            <p class="mb-2">将图片拖到此处，或<button type="button" class="btn btn-link p-0" onclick="document.getElementById('uploadInput').click()">点击上传</button></p>
            <small class="text-muted">只能上传jpg/png文件</small>
          </div>
          <input type="file" id="uploadInput" accept="image/*" multiple style="display: none;" onchange="ImageManager.handleFileSelect(event)">
        </div>

        <!-- 文件列表 -->
        <div id="uploadFileList" class="mt-3">
          <!-- 文件列表将通过JavaScript动态生成 -->
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="ImageManager.uploadImages()" id="uploadImagesBtn">
          <span class="spinner-border spinner-border-sm d-none" id="uploadSpinner"></span>
          上传
        </button>
      </div>
    </div>
  </div>
</div>

<!-- 替换图片对话框 -->
<div class="modal fade" id="replaceModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">替换图片</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="upload-area border border-2 border-dashed rounded p-4 text-center"
             id="replaceArea"
             ondrop="ImageManager.handleReplaceDrop(event)"
             ondragover="ImageManager.handleDragOver(event)"
             ondragleave="ImageManager.handleDragLeave(event)">
          <i class="bi bi-arrow-repeat" style="font-size: 3rem; color: #6c757d;"></i>
          <div class="mt-3">
            <p class="mb-2">选择新图片替换</p>
            <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('replaceInput').click()">选择文件</button>
            <small class="d-block mt-2 text-muted">只能上传jpg/png文件</small>
          </div>
          <input type="file" id="replaceInput" accept="image/*" style="display: none;" onchange="ImageManager.handleReplaceFileSelect(event)">
        </div>

        <!-- 替换预览 -->
        <div id="replacePreview" class="mt-3 d-none">
          <!-- 预览将通过JavaScript动态生成 -->
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="ImageManager.confirmReplace()" id="confirmReplaceBtn">
          <span class="spinner-border spinner-border-sm d-none" id="replaceSpinner"></span>
          确认替换
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="/frontend/static/js/image-management.js"></script>
{% endblock %}
