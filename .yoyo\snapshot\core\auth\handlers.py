"""
认证API处理器
处理认证相关的HTTP请求
"""

from typing import Dict, Any
from fastapi import HTTPException

from .services import auth_service
from ..knowledge_base.schemas import ApiResponse, LoginRequest, LoginResponse


async def login(body: dict) -> LoginResponse:
    """用户登录"""
    try:
        username = body.get("username", "")
        password = body.get("password", "")
        
        if not username or not password:
            return LoginResponse(code=400, message="用户名和密码不能为空")
        
        result = auth_service.login(username, password)
        
        return LoginResponse(
            code=result["code"],
            message=result["message"],
            token=result.get("token")
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
