"""
数据集仓库
处理数据集相关的数据访问
"""

from typing import List, Dict, Any
from bson.objectid import ObjectId
from pymongo.database import Database
from ..connection import get_database
from ...config.settings import settings


class DatasetRepository:
    """数据集数据访问仓库"""
    
    def __init__(self, db: Database = None):
        self.db = db or get_database()
        self.collection = self.db['datasets']
    
    def get_datasets_by_parent_id(self, parent_id: str) -> List[Dict[str, Any]]:
        """根据父级ID获取数据集列表"""
        return list(self.collection.find({"parentId": ObjectId(parent_id)}))
    
    def get_dataset_by_id(self, dataset_id: str) -> Dict[str, Any]:
        """根据ID获取单个数据集"""
        return self.collection.find_one({"_id": ObjectId(dataset_id)})
    
    def get_datasets_by_ids(self, dataset_ids: List[ObjectId]) -> List[Dict[str, Any]]:
        """根据ID列表获取数据集"""
        return list(self.collection.find({"_id": {"$in": dataset_ids}}))
    
    def get_datasets_dict(self, dataset_ids: List[ObjectId]) -> Dict[str, str]:
        """获取数据集ID到名称的映射字典"""
        datasets = self.get_datasets_by_ids(dataset_ids)
        return {str(d["_id"]): d["name"] for d in datasets}
    
    def get_dataset_intro_dict(self, parent_id: str) -> Dict[str, str]:
        """获取数据集名称到介绍的映射字典"""
        datasets = self.get_datasets_by_parent_id(parent_id)
        return {d["name"]: d["intro"] for d in datasets}


# 创建全局仓库实例
dataset_repository = DatasetRepository()
