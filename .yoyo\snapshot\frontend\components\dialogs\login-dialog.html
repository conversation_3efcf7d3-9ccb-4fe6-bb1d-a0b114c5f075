<!-- 登录对话框组件 -->
<el-dialog
  title="登录"
  :visible.sync="loginDialogVisible"
  width="400px"
  :close-on-click-modal="false">
  
  <el-form :model="loginForm" ref="loginForm" label-width="80px">
    <el-form-item label="用户名" prop="username" :rules="[{required: true, message: '请输入用户名', trigger: 'blur'}]">
      <el-input v-model="loginForm.username" placeholder="请输入用户名"></el-input>
    </el-form-item>
    
    <el-form-item label="密码" prop="password" :rules="[{required: true, message: '请输入密码', trigger: 'blur'}]">
      <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" @keyup.enter.native="handleLogin"></el-input>
    </el-form-item>
  </el-form>
  
  <div slot="footer" class="dialog-footer">
    <el-button @click="loginDialogVisible = false">取消</el-button>
    <el-button type="primary" @click="handleLogin" :loading="loginLoading">登录</el-button>
  </div>
</el-dialog>
