/**
 * 图片管理 - JavaScript (原生实现)
 */

// 图片管理器
const ImageManager = {
  // 应用状态
  state: {
    // 基础数据
    datasets: [],
    collections: [],
    selectedDataset: '',
    selectedCollection: '',
    loading: false,

    // 图片数据
    imageDataList: [],

    // 编辑对话框
    editForm: {
      id: '',
      q: '',
      a: ''
    },
    saving: false,

    // 图片预览
    previewImageSrc: '',

    // 上传图片
    uploadFileList: [],
    uploading: false,

    // 替换图片
    replaceFileList: [],
    replacing: false,
    currentReplaceItem: null,
    currentReplaceIndex: -1,

    // 侧边栏状态
    activeMenu: '3',
    sfilesh: false
  },

  // 初始化应用
  async init() {
    await this.initializeApp();
    this.bindEvents();
  },

  // 绑定事件
  bindEvents() {
    // 绑定拖拽事件
    this.setupDragAndDrop();
  },

  /**
   * 初始化应用
   */
  async initializeApp() {
    await this.loadDatasets();
    if (this.state.datasets.length > 0) {
      this.state.selectedDataset = this.state.datasets[0].id;
      await this.loadCollections();
    }
  },

  /**
   * 加载数据集列表
   */
  async loadDatasets() {
    try {
      const response = await ApiClient.get('/getDatasList/');
      if (response.code === 200) {
        this.state.datasets = response.data;
        this.updateDatasetSelect();
      }
    } catch (error) {
      console.error('加载数据集失败:', error);
      showError('加载数据集失败');
    }
  },

  /**
   * 更新数据集下拉框
   */
  updateDatasetSelect() {
    const select = document.getElementById('selectedDataset');
    if (!select) return;

    // 清空现有选项
    select.innerHTML = '<option value="">选择数据集</option>';

    // 添加数据集选项
    this.state.datasets.forEach(dataset => {
      const option = document.createElement('option');
      option.value = dataset.id;
      option.textContent = dataset.name;
      select.appendChild(option);
    });
  },

  /**
   * 加载集合列表
   */
  async loadCollections() {
    const selectedDataset = document.getElementById('selectedDataset').value;
    if (!selectedDataset) return;

    this.state.selectedDataset = selectedDataset;

    try {
      const response = await ApiClient.get('/getcollectionsList/', {
        datasetId: selectedDataset
      });
      if (response.code === 200) {
        this.state.collections = response.data;
        this.updateCollectionSelect();
        if (this.state.collections.length > 0) {
          this.state.selectedCollection = this.state.collections[0].id;
          document.getElementById('selectedCollection').value = this.state.selectedCollection;
          await this.loadImageData();
        }
      }
    } catch (error) {
      console.error('加载集合列表失败:', error);
      showError('加载集合列表失败');
    }
  },

  /**
   * 更新集合下拉框
   */
  updateCollectionSelect() {
    const select = document.getElementById('selectedCollection');
    if (!select) return;

    // 清空现有选项
    select.innerHTML = '<option value="">选择集合</option>';

    // 添加集合选项
    this.state.collections.forEach(collection => {
      const option = document.createElement('option');
      option.value = collection.id;
      option.textContent = collection.name;
      select.appendChild(option);
    });
  },

  /**
   * 加载图片数据
   */
  async loadImageData() {
    const selectedCollection = document.getElementById('selectedCollection').value;
    if (!selectedCollection) return;

    this.state.selectedCollection = selectedCollection;
    this.state.loading = true;
    this.showLoading(true);

    try {
      const response = await ApiClient.get('/getDatasetsDatas_images/', {
        collectionId: selectedCollection
      });

      if (response.code === 200) {
        this.state.imageDataList = response.data.map(item => ({
          id: item[0],
          q: item[1],
          a: item[2] || '',
          processedQ: this.processImageContent(item[1]),
          images: this.extractImages(item[1])
        }));

        this.renderImageData();
      }
    } catch (error) {
      console.error('加载图片数据失败:', error);
      showError('加载图片数据失败');
    } finally {
      this.state.loading = false;
      this.showLoading(false);
    }
  },

  /**
   * 显示/隐藏加载状态
   */
  showLoading(show) {
    const spinner = document.getElementById('querySpinner');
    const btn = document.getElementById('queryBtn');

    if (spinner && btn) {
      if (show) {
        spinner.classList.remove('d-none');
        btn.disabled = true;
      } else {
        spinner.classList.add('d-none');
        btn.disabled = false;
      }
    }
  },
        
        /**
         * 处理图片内容
         */
        processImageContent(content) {
          if (!content) return '';
          
          // 将Markdown图片格式转换为HTML
          const imagePattern = /!\[([^\]]*)\]\(([^)]+)\)/g;
          return content.replace(imagePattern, (match, alt, src) => {
            if (src.includes('/api/system/img/')) {
              return `<img src="http://171.43.138.237:3000${src}" alt="${alt}" style="max-width: 200px; height: auto; margin: 5px;" loading="lazy" referrerpolicy="no-referrer"/>`;
            }
            return match;
          });
        },

  /**
   * 渲染图片数据
   */
  renderImageData() {
    const container = document.getElementById('imageDataList');
    const emptyState = document.getElementById('emptyState');
    const imageDataContainer = document.getElementById('imageDataContainer');

    if (this.state.imageDataList.length === 0) {
      emptyState.classList.remove('d-none');
      imageDataContainer.classList.add('d-none');
      return;
    }

    emptyState.classList.add('d-none');
    imageDataContainer.classList.remove('d-none');

    // 清空容器
    container.innerHTML = '';

    // 渲染每个图片数据项
    this.state.imageDataList.forEach((item, index) => {
      const itemHtml = this.createImageItemHtml(item, index);
      container.insertAdjacentHTML('beforeend', itemHtml);
    });
  },

  /**
   * 创建图片项HTML
   */
  createImageItemHtml(item, index) {
    const imagesHtml = item.images.length > 0 ? `
      <div class="images-section mt-3">
        <h6>相关图片：</h6>
        <div class="images-grid row g-2">
          ${item.images.map((image, imgIndex) => `
            <div class="col-6 col-md-4 col-lg-3">
              <div class="image-item position-relative">
                <img src="${image.src}"
                     onclick="ImageManager.previewImage('${image.src}')"
                     class="img-thumbnail w-100"
                     style="height: 120px; object-fit: cover; cursor: pointer;">
                <div class="image-actions position-absolute top-0 end-0 p-1">
                  <div class="btn-group-vertical">
                    <button type="button" class="btn btn-sm btn-outline-primary"
                            onclick="ImageManager.replaceImage(${index}, ${imgIndex})"
                            title="替换">
                      <i class="bi bi-arrow-repeat"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger"
                            onclick="ImageManager.deleteImage(${index}, ${imgIndex})"
                            title="删除">
                      <i class="bi bi-trash"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    ` : '';

    return `
      <div class="col-12 col-lg-6 col-xl-4">
        <div class="card h-100 image-data-item" data-index="${index}">
          <div class="card-header d-flex justify-content-between align-items-center">
            <span class="data-id text-muted small">ID: ${item.id}</span>
            <button type="button" class="btn btn-outline-primary btn-sm"
                    onclick="ImageManager.editData(${index})">
              <i class="bi bi-pencil"></i> 编辑
            </button>
          </div>

          <div class="card-body">
            <div class="question-section mb-3">
              <h6>问题内容：</h6>
              <div class="content-display p-2 bg-light rounded">${item.processedQ}</div>
            </div>

            ${item.a ? `
              <div class="answer-section mb-3">
                <h6>答案内容：</h6>
                <div class="content-display p-2 bg-light rounded">${item.a}</div>
              </div>
            ` : ''}

            ${imagesHtml}
          </div>
        </div>
      </div>
    `;
  },

  /**
   * 处理图片内容
   */
  processImageContent(content) {
    if (!content) return '';

    const imagePattern = /!\[([^\]]*)\]\(([^)]+)\)/g;
    return content.replace(imagePattern, (match, alt, src) => {
      if (src.includes('/api/system/img/')) {
        return `<img src="http://171.43.138.237:3000${src}" alt="${alt}" style="max-width: 200px; height: auto; margin: 5px;" loading="lazy" referrerpolicy="no-referrer"/>`;
      }
      return match;
    });
  },

  /**
   * 提取图片信息
   */
  extractImages(content) {
    if (!content) return [];

    const images = [];
    const imagePattern = /!\[([^\]]*)\]\(([^)]+)\)/g;
    let match;

    while ((match = imagePattern.exec(content)) !== null) {
      const [fullMatch, alt, src] = match;
      if (src.includes('/api/system/img/')) {
        const picId = src.split('img/')[1].split('.')[0];
        images.push({
          alt: alt,
          src: `http://171.43.138.237:3000${src}`,
          picId: picId,
          originalMatch: fullMatch
        });
      }
    }

    return images;
  },

  /**
   * 编辑数据
   */
  editData(index) {
    const item = this.state.imageDataList[index];
    this.state.editForm = {
      id: item.id,
      q: item.q,
      a: item.a
    };

    // 填充表单
    document.getElementById('editQ').value = item.q;
    document.getElementById('editA').value = item.a || '';

    // 显示对话框
    const modal = new bootstrap.Modal(document.getElementById('editModal'));
    modal.show();
  },

  /**
   * 保存编辑
   */
  async saveEdit() {
    const q = document.getElementById('editQ').value;
    const a = document.getElementById('editA').value;

    this.state.saving = true;
    this.showSaveEditLoading(true);

    try {
      const response = await ApiClient.post('/updateDatasetdatas/', {
        data: [this.state.editForm.id, q, a, []]
      });

      if (response.code === 200) {
        showSuccess('保存成功');
        const modal = bootstrap.Modal.getInstance(document.getElementById('editModal'));
        modal.hide();
        await this.loadImageData();
      } else {
        showError('保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      showError('保存失败');
    } finally {
      this.state.saving = false;
      this.showSaveEditLoading(false);
    }
  },

  /**
   * 显示/隐藏保存编辑加载状态
   */
  showSaveEditLoading(show) {
    const spinner = document.getElementById('saveEditSpinner');
    const btn = document.getElementById('saveEditBtn');

    if (spinner && btn) {
      if (show) {
        spinner.classList.remove('d-none');
        btn.disabled = true;
      } else {
        spinner.classList.add('d-none');
        btn.disabled = false;
      }
    }
  },

  /**
   * 预览图片
   */
  previewImage(src) {
    document.getElementById('previewImage').src = src;
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
  },

  /**
   * 显示上传对话框
   */
  showUploadDialog() {
    this.state.uploadFileList = [];
    this.updateUploadFileList();
    const modal = new bootstrap.Modal(document.getElementById('uploadModal'));
    modal.show();
  },

  /**
   * 设置拖拽上传
   */
  setupDragAndDrop() {
    // 这个方法在初始化时调用，设置拖拽事件
  },

  /**
   * 处理拖拽悬停
   */
  handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('border-primary');
  },

  /**
   * 处理拖拽离开
   */
  handleDragLeave(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('border-primary');
  },

  /**
   * 处理文件拖拽
   */
  handleDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('border-primary');

    const files = Array.from(event.dataTransfer.files);
    this.addFilesToUploadList(files);
  },

  /**
   * 处理文件选择
   */
  handleFileSelect(event) {
    const files = Array.from(event.target.files);
    this.addFilesToUploadList(files);
  },

  /**
   * 添加文件到上传列表
   */
  addFilesToUploadList(files) {
    const imageFiles = files.filter(file => file.type.startsWith('image/'));

    if (imageFiles.length !== files.length) {
      showWarning('只能上传图片文件');
    }

    this.state.uploadFileList = [...this.state.uploadFileList, ...imageFiles];
    this.updateUploadFileList();
  },

  /**
   * 更新上传文件列表显示
   */
  updateUploadFileList() {
    const container = document.getElementById('uploadFileList');
    if (!container) return;

    if (this.state.uploadFileList.length === 0) {
      container.innerHTML = '';
      return;
    }

    const listHtml = this.state.uploadFileList.map((file, index) => `
      <div class="d-flex align-items-center justify-content-between p-2 border rounded mb-2">
        <div class="d-flex align-items-center">
          <i class="bi bi-image me-2"></i>
          <span>${file.name}</span>
          <small class="text-muted ms-2">(${formatFileSize(file.size)})</small>
        </div>
        <button type="button" class="btn btn-sm btn-outline-danger"
                onclick="ImageManager.removeUploadFile(${index})">
          <i class="bi bi-x"></i>
        </button>
      </div>
    `).join('');

    container.innerHTML = listHtml;
  },

  /**
   * 移除上传文件
   */
  removeUploadFile(index) {
    this.state.uploadFileList.splice(index, 1);
    this.updateUploadFileList();
  },

  /**
   * 上传图片
   */
  async uploadImages() {
    if (this.state.uploadFileList.length === 0) {
      showWarning('请先选择图片');
      return;
    }

    this.state.uploading = true;
    this.showUploadLoading(true);

    try {
      for (const file of this.state.uploadFileList) {
        const formData = new FormData();
        formData.append('file', file);

        const response = await ApiClient.upload('/uploadimage/', formData);
        if (response.code !== 200) {
          showError(`图片 ${file.name} 上传失败`);
        }
      }

      showSuccess('图片上传成功');
      const modal = bootstrap.Modal.getInstance(document.getElementById('uploadModal'));
      modal.hide();
      await this.loadImageData();
    } catch (error) {
      console.error('上传图片失败:', error);
      showError('上传图片失败');
    } finally {
      this.state.uploading = false;
      this.showUploadLoading(false);
    }
  },

  /**
   * 显示/隐藏上传加载状态
   */
  showUploadLoading(show) {
    const spinner = document.getElementById('uploadSpinner');
    const btn = document.getElementById('uploadImagesBtn');

    if (spinner && btn) {
      if (show) {
        spinner.classList.remove('d-none');
        btn.disabled = true;
      } else {
        spinner.classList.add('d-none');
        btn.disabled = false;
      }
    }
  },

  /**
   * 替换图片
   */
  replaceImage(itemIndex, imageIndex) {
    const item = this.state.imageDataList[itemIndex];
    this.state.currentReplaceItem = item;
    this.state.currentReplaceIndex = imageIndex;
    this.state.replaceFileList = [];

    // 清空预览
    document.getElementById('replacePreview').classList.add('d-none');

    // 显示对话框
    const modal = new bootstrap.Modal(document.getElementById('replaceModal'));
    modal.show();
  },

  /**
   * 处理替换图片拖拽
   */
  handleReplaceDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('border-primary');

    const files = Array.from(event.dataTransfer.files);
    if (files.length > 0 && files[0].type.startsWith('image/')) {
      this.setReplaceFile(files[0]);
    } else {
      showWarning('请选择图片文件');
    }
  },

  /**
   * 处理替换图片文件选择
   */
  handleReplaceFileSelect(event) {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      this.setReplaceFile(file);
    } else {
      showWarning('请选择图片文件');
    }
  },

  /**
   * 设置替换文件
   */
  setReplaceFile(file) {
    this.state.replaceFileList = [file];

    // 显示预览
    const reader = new FileReader();
    reader.onload = (e) => {
      const previewContainer = document.getElementById('replacePreview');
      previewContainer.innerHTML = `
        <div class="text-center">
          <img src="${e.target.result}" class="img-thumbnail" style="max-height: 200px;">
          <div class="mt-2">
            <small class="text-muted">${file.name} (${formatFileSize(file.size)})</small>
          </div>
        </div>
      `;
      previewContainer.classList.remove('d-none');
    };
    reader.readAsDataURL(file);
  },

  /**
   * 确认替换图片
   */
  async confirmReplace() {
    if (this.state.replaceFileList.length === 0) {
      showWarning('请先选择新图片');
      return;
    }

    this.state.replacing = true;
    this.showReplaceLoading(true);

    try {
      // 这里实现图片替换逻辑
      // 实际项目中需要调用相应的API
      showSuccess('图片替换成功');
      const modal = bootstrap.Modal.getInstance(document.getElementById('replaceModal'));
      modal.hide();
      await this.loadImageData();
    } catch (error) {
      console.error('替换图片失败:', error);
      showError('替换图片失败');
    } finally {
      this.state.replacing = false;
      this.showReplaceLoading(false);
    }
  },

  /**
   * 显示/隐藏替换加载状态
   */
  showReplaceLoading(show) {
    const spinner = document.getElementById('replaceSpinner');
    const btn = document.getElementById('confirmReplaceBtn');

    if (spinner && btn) {
      if (show) {
        spinner.classList.remove('d-none');
        btn.disabled = true;
      } else {
        spinner.classList.add('d-none');
        btn.disabled = false;
      }
    }
  },

  /**
   * 删除图片
   */
  async deleteImage(itemIndex, imageIndex) {
    try {
      await confirmDialog('确定要删除这张图片吗？', '确认删除');

      // 这里实现图片删除逻辑
      // 实际项目中需要调用相应的API
      showSuccess('图片删除成功');
      await this.loadImageData();
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除图片失败:', error);
        showError('删除图片失败');
      }
    }
  },

  /**
   * 处理菜单选择
   */
  handleMenuSelect(index) {
    if (index === '2') {
      window.router.navigate('knowledge-base');
    } else if (index === '1') {
      window.router.navigate('chat-logs');
    } else if (index === '3') {
      this.state.activeMenu = index;
    } else if (index === '4') {
      window.router.navigate('audit');
    }
  }
};

// 图片管理应用组件（兼容旧的路由系统）
const ImageManagementApp = {
  init() {
    ImageManager.init();
  }
};

// 导出到全局
window.ImageManager = ImageManager;
window.ImageManagementApp = ImageManagementApp;
