<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库管理系统</title>
    
    <!-- Element UI CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    
    <!-- 公共样式 -->
    <link rel="stylesheet" href="/frontend/static/css/common.css">
    
    <style>
        /* 初始加载样式 */
        #initial-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            color: white;
        }
        
        .loading-content {
            text-align: center;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            font-size: 18px;
            font-weight: 500;
        }
        
        .loading-subtext {
            font-size: 14px;
            opacity: 0.8;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <!-- 初始加载界面 -->
    <div id="initial-loading">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">知识库管理系统</div>
            <div class="loading-subtext">正在加载中...</div>
        </div>
    </div>

    <!-- 主应用容器 -->
    <div id="app" style="display: none;">
        <!-- 内容将通过路由系统动态加载 -->
    </div>

    <!-- 核心依赖 -->
    <script src="/media/js/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="/media/js/axios.min.js"></script>
    <script src="/media/js/dayjs.min.js"></script>
    
    <!-- 公共工具 -->
    <script src="/frontend/static/js/common.js"></script>
    
    <!-- 路由系统 -->
    <script src="/frontend/static/js/router.js"></script>
    
    <script>
        // 应用初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟隐藏加载界面，确保所有资源加载完成
            setTimeout(() => {
                const initialLoading = document.getElementById('initial-loading');
                const app = document.getElementById('app');
                
                if (initialLoading && app) {
                    initialLoading.style.display = 'none';
                    app.style.display = 'block';
                }
                
                // 初始化路由系统
                if (window.router) {
                    console.log('路由系统已初始化');
                } else {
                    console.error('路由系统初始化失败');
                }
            }, 1000);
        });
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            console.error('全局错误:', event.error);
            
            // 隐藏加载界面
            const initialLoading = document.getElementById('initial-loading');
            if (initialLoading) {
                initialLoading.style.display = 'none';
            }
            
            // 显示错误信息
            const app = document.getElementById('app');
            if (app) {
                app.style.display = 'block';
                app.innerHTML = `
                    <div style="
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        height: 100vh;
                        flex-direction: column;
                        color: #666;
                    ">
                        <i class="el-icon-warning" style="font-size: 64px; color: #F56C6C; margin-bottom: 20px;"></i>
                        <h2>系统加载失败</h2>
                        <p>请刷新页面重试，或联系管理员</p>
                        <button onclick="location.reload()" style="
                            margin-top: 20px;
                            padding: 10px 20px;
                            background: #409EFF;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                        ">刷新页面</button>
                    </div>
                `;
            }
        });
        
        // 兼容性检查
        function checkCompatibility() {
            const features = [
                'Promise',
                'fetch',
                'Map',
                'Set'
            ];
            
            const unsupported = features.filter(feature => !window[feature]);
            
            if (unsupported.length > 0) {
                console.warn('浏览器兼容性警告，不支持的特性:', unsupported);
                
                // 可以在这里添加polyfill或显示兼容性警告
                if (window.ELEMENT && window.ELEMENT.Message) {
                    window.ELEMENT.Message.warning('您的浏览器版本较低，可能影响使用体验');
                }
            }
        }
        
        // 执行兼容性检查
        checkCompatibility();
    </script>
</body>
</html>
