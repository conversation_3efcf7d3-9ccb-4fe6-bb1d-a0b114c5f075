"""
聊天日志API处理器
处理聊天日志相关的HTTP请求
"""

from typing import Dict, Any
from fastapi import HTT<PERSON>Exception, Request
from fastapi.responses import H<PERSON>LResponse

from .services import chat_logs_service


async def get_app_list() -> Dict[str, Any]:
    """获取应用列表API"""
    try:
        result = chat_logs_service.get_app_list()
        if result["code"] == 200:
            return result
        else:
            raise HTTPException(status_code=500, detail=result.get("message", "获取应用列表失败"))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def get_chat_logs(date_start: str, date_end: str, page_num: int, app_id: str) -> Dict[str, Any]:
    """获取聊天日志API"""
    try:
        result = chat_logs_service.get_chat_logs(date_start, date_end, page_num, app_id)
        if result["code"] == 200:
            return result
        else:
            raise HTTPException(status_code=500, detail=result.get("message", "获取聊天日志失败"))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def get_pagination_records(data: Dict[str, Any]) -> Dict[str, Any]:
    """获取分页记录API"""
    try:
        result = chat_logs_service.get_pagination_records(data)
        if result["code"] == 200:
            return result
        else:
            raise HTTPException(status_code=500, detail=result.get("message", "获取分页记录失败"))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def save_feedbacks(body: Dict[str, Any]) -> Dict[str, Any]:
    """保存反馈API"""
    try:
        chat_id = body.get("chatId", "")
        feedback_type = body.get("type", "")
        content = body.get("content", "")
        
        if not chat_id or not feedback_type:
            raise HTTPException(status_code=400, detail="缺少必要参数")
        
        result = chat_logs_service.save_feedbacks(chat_id, feedback_type, content)
        if result["code"] == 200:
            return result
        else:
            raise HTTPException(status_code=500, detail=result.get("message", "保存反馈失败"))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 保持兼容性的页面处理器（将被前端模板替代）
async def h5_log_page(request: Request) -> HTMLResponse:
    """聊天日志页面（兼容性）"""
    # 重定向到新的前端结构
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>重定向中...</title>
        <script>
            window.location.href = '/#/chat-logs';
        </script>
    </head>
    <body>
        <p>页面重定向中，请稍候...</p>
        <p><a href="/#/chat-logs">如果没有自动跳转，请点击这里</a></p>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content, status_code=200)
