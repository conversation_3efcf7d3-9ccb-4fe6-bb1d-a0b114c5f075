{% extends "base.html" %}

{% block title %}知识库管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/frontend/static/css/knowledge-base.css">
{% endblock %}

{% block content %}
<el-container class="main-container">
  <!-- 侧边栏导航 -->
  {% include 'components/layout/sidebar.html' %}

  <!-- 右侧内容 -->
  <el-main style="padding:0">
    <!-- 顶部工具栏 -->
    {% include 'components/layout/toolbar.html' %}
    
    <!-- 卡片展示区 -->
    <div class="card-box">
      <el-row :gutter="20" v-if="activeMenu==2">
        <el-col
          v-for="(file, index) in filteredFiles"
          :key="index"
          :xs="12" :sm="8" :md="6" :lg="6" :xl="4">
          {% include 'components/cards/file-card.html' %}
        </el-col>
      </el-row>
      
      <!-- 空状态 -->
      <div v-if="filteredFiles.length === 0 && !loading" style="text-align: center; padding: 50px;">
        <i class="el-icon-folder-opened" style="font-size: 64px; color: #ddd;"></i>
        <p style="color: #999; margin-top: 20px;">暂无数据</p>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="loading" style="text-align: center; padding: 50px;">
        <i class="el-icon-loading" style="font-size: 32px; color: #409EFF;"></i>
        <p style="color: #666; margin-top: 20px;">加载中...</p>
      </div>
    </div>
  </el-main>
</el-container>

<!-- 对话框组件 -->
{% include 'components/dialogs/login-dialog.html' %}
{% include 'components/dialogs/upload-dialog.html' %}
{% include 'components/dialogs/preview-dialog.html' %}

<!-- 删除确认对话框 -->
<el-dialog
  title="确认删除"
  :visible.sync="deleteDialogVisible"
  width="400px">
  <p>确定要删除文件 "{{ currentFile.name }}" 吗？此操作不可撤销。</p>
  <div slot="footer" class="dialog-footer">
    <el-button @click="deleteDialogVisible = false">取消</el-button>
    <el-button type="danger" @click="confirmDelete" :loading="loading">确定删除</el-button>
  </div>
</el-dialog>

<!-- 审核对话框 -->
<el-dialog
  title="文件审核"
  :visible.sync="auditDialogVisible"
  width="80%"
  class="audit-dialog">
  <div class="audit-dialog-content">
    <el-alert
      title="审核说明"
      description="请仔细检查文件内容和相同型号的文件，确认无误后进行审核。"
      type="info"
      :closable="false"
      style="margin-bottom: 20px;">
    </el-alert>
    
    <h4>相同型号文件列表</h4>
    <div class="same-files-table-container">
      <el-table
        :data="sameFiles"
        style="width: 100%"
        height="300">
        <el-table-column prop="name" label="文件名" min-width="200"></el-table-column>
        <el-table-column prop="time" label="创建时间" width="180" :formatter="formatTime"></el-table-column>
        <el-table-column prop="audit" label="审核状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.audit === '1' ? 'success' : 'warning'">
              {{ scope.row.audit === '1' ? '已审核' : '未审核' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
  
  <div slot="footer" class="audit-dialog-footer">
    <el-button @click="auditDialogVisible = false">取消</el-button>
    <el-button type="primary" @click="confirmAudit" :loading="auditing">通过审核</el-button>
  </div>
</el-dialog>

<!-- 文件信息对话框 -->
<el-dialog
  title="文件信息"
  :visible.sync="fileInfoDialogVisible"
  width="500px">
  <el-form :model="fileInfoForm" label-width="100px">
    <el-form-item label="产品型号">
      <el-input v-model="fileInfoForm.型号"></el-input>
    </el-form-item>
    <el-form-item label="产品名称">
      <el-input v-model="fileInfoForm.名称"></el-input>
    </el-form-item>
    <el-form-item label="售前售后">
      <el-select v-model="fileInfoForm.售前售后" style="width: 100%;">
        <el-option label="售前" value="售前"></el-option>
        <el-option label="售后" value="售后"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="可接入软件">
      <el-input v-model="fileInfoForm.可接入软件"></el-input>
    </el-form-item>
  </el-form>
  
  <div slot="footer" class="dialog-footer">
    <el-button @click="fileInfoDialogVisible = false">取消</el-button>
    <el-button type="primary" @click="confirmFileInfo">确定</el-button>
  </div>
</el-dialog>
{% endblock %}

{% block extra_js %}
<script src="/frontend/static/js/knowledge-base.js"></script>
{% endblock %}
