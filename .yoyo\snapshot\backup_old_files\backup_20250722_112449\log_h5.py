import os
import datetime
from bson import ObjectId
from bson.objectid import ObjectId
from fastapi import Request, HTTPException
from fastapi.responses import HTMLResponse
from config import init_monogdb

async def getChatLogs(dateStart: str, dateEnd: str, pageNum: int, appId: str):
    try:
        db = init_monogdb()
        dateEnd = dateEnd.replace('00:00:00', '23:59:59')
        dateStart = datetime.datetime.strptime(dateStart, '%Y-%m-%dT%H:%M:%S.000z')
        dateEnd = datetime.datetime.strptime(dateEnd, '%Y-%m-%dT%H:%M:%S.000z')
        # 构建查询条件
        query = {
            "appId": ObjectId(appId),
            "$and": [
                { "updateTime": { "$gte": dateStart } },
                { "updateTime": { "$lte": dateEnd } }
            ]
        }
        # 执行查询并排序
        dataset_datas = db['chats'].find(query).sort("updateTime", 1)
        dataset_datas = [{"id":dd["chatId"],"time":dd["updateTime"]} for dd in dataset_datas]
        dataset_datas = {"code":200,"data":{"total":len(dataset_datas),"data":dataset_datas}}
        return dataset_datas
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

def get_response_ywk(dd):
    try:
        responseData = dd['responseData']
        for r in responseData:
            if r.get("moduleName") == "提取业务信息":
                return r["customOutputs"]["result"]
    except Exception as e:
        pass
    return ""


async def getPaginationRecords(data: dict):
    try:
        db = init_monogdb()
        # 构建查询条件
        dateStart = data["dateStart"]
        dateEnd = data["dateEnd"]
        dateEnd = dateEnd.replace('00:00:00', '23:59:59')
        dateStart = datetime.datetime.strptime(dateStart, '%Y-%m-%dT%H:%M:%S.000z')
        dateEnd = datetime.datetime.strptime(dateEnd, '%Y-%m-%dT%H:%M:%S.000z')
        query = {"appId": ObjectId(data["appId"]),
                 "$and": [
                        { "time": { "$gte": dateStart } },
                        { "time": { "$lte": dateEnd } }
                    ]
                }
        isread = int(data.get("isRead","2") or 2)
        if isread == 1:
            query["isread"] = isread
        elif isread == 0:
            query["isread"] = None
        else:
            pass
        # 执行查询并排序
	   # 使用聚合管道
        pipeline = [
		    {"$match": query},
		    {"$sort": { "chatId": 1, "time": 1 , "obj":-1}},
		    {
		        "$group": {
		            "_id": "$chatId",  # 按 chatId 分组
		            "chatItems": { "$push": "$$ROOT" }  # 将该 chatId 的所有文档聚合成一个数组
		                                                # 由于前一步的 $sort，chatItems 数组中的元素会按 time 排序
		        }
		    },{"$sort": { "_id": 1 }}]
        dataset_datas = db['chatitems'].aggregate(pipeline, allowDiskUse=True)
        
        #dataset_datas = db['chatitems'].find(query).sort("time", 1).allow_disk_use(True)
        dataset_datas_list = []
        for ddd in dataset_datas:
            dds = ddd["chatItems"]
            for dd in dds:
                for k,v in dd.items():
                    if type(v) == ObjectId:
                        dd[k] = str(v)
                    elif type(v) == datetime.datetime:
                        dd[k] = str(v)
                    if k == 'value':
                        newlist = []
                        for vv in v:
                            if vv.get('type', '') != 'text':
                                continue
                            else:
                                newlist.append(vv)
                        dd[k] = newlist
                ywk = get_response_ywk(dd)
                dd["ywk"] = ywk
                if 'responseData' in dd:
                    del dd['responseData']
                dataset_datas_list.append(dd)
        dataset_datas_new = {"code":200,"data":{"list":dataset_datas_list}}
        return dataset_datas_new
    except Exception as e:
        print('e--------------=',e)
        raise HTTPException(status_code=500, detail=str(e))



async def saveFeedbacks(body: dict):
    try:
        db = init_monogdb()
        # 构建查询条件
        if body.get("type") == 1:
            update = {"$set": {"isread": 1}}
            ress = db['chatitems'].find({"chatId": body["chatId"]}).sort("time", 1)
            temid = ObjectId(body["id"])
            for res in ress:
                if res["obj"] == "Human":
                    temid = res["_id"]
                if str(res["_id"]) == body["id"]:
                    query = {"_id": {"$in": [temid, ObjectId(body["id"])]}}
                    break
            result = db['chatitems'].update_many(query, update)
            return {"code":200}
        else:
            query = {"_id": ObjectId(body["id"])}
            update = {"$set": {"userBadFeedback": body["fix"]}}
        # 执行查询并排序
        dataset_datas = db['chatitems'].update_one(query, update)
        if dataset_datas.modified_count > 0:
            dataset_datas_new = {"code":200}
        else:
            dataset_datas_new = {"code":520}
        return dataset_datas_new
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def applist(request: Request):
     # 读取 HTML 文件内容
    db = init_monogdb()
    apps = db['apps'].find().sort("updateTime", 1)
    data = []
    for app in apps:
        data.append({"_id":str(app["_id"]),"name":app["name"]})
    return {"code":200,"data":data}

async def h5_log(request: Request):
     # 读取 HTML 文件内容
    with open(os.path.join("media", "index_log.html"), "r", encoding="utf-8") as file:
        html_content = file.read()
    return HTMLResponse(content=html_content, status_code=200)